const gandalfBFFHost = 'https://gandalf.globo.com/adcore/v1/proxy-pass'
const adstudioBase = 'https://gandalf.globo.com/adcore/v1/adstudio'

export const environment = {
  production: true,
  sentryDsn: 'https://<EMAIL>/1111', // Sentry PROD
  name: 'production',
  version: '__VERSION__',
  api: {
    validarToken:
      'https://backend.portalagencias.stg.apps.tvglobo.com.br/api/Login/Autorizacao',
    getAgenciaUsuario: `${gandalfBFFHost}/agencia/usuario`,
    getAgenciaByName: `${gandalfBFFHost}/agencia/sigla`,
    getAgenciaByCNPJ: `${gandalfBFFHost}/agencia/agencia-cpfcnpj`,
    getAgenciasByCNPJ: `${gandalfBFFHost}/agencia-cliente`,
    generateToken: `${gandalfBFFHost}/login/generate-token`,
    getAutorizacoes: `${gandalfBFFHost}/login/authorizations`,
    refreshToken:
      'https://api.prd.negocios.tvglobo.com.br/auth/realms/globoads/tvg-token/token',
    revokeToken: `${gandalfBFFHost}/login/v3/oauth/revoke`,
    repositorioImagem:
      'https://imagens.prd.negocios.tvglobo.com.br/{CATEGORIA}/{SIGLA}/{DIMENSAO}.png',
    getPreco: `${gandalfBFFHost}/tabelapreco/precos/abrangencias`,
    getCidades: `${gandalfBFFHost}/public/simulador-midia/sua-globo/localizar`,
    getInfoExibidora: `${gandalfBFFHost}/public/simulador-midia/sua-globo`,
    simulacao: `${gandalfBFFHost}/public/simulador-midia/simulacao`,
    cadastroSimulacao: `${gandalfBFFHost}/public/simulador-midia/registro`,
    getProgramasPUBLIC: `${gandalfBFFHost}/public/programa/api/v1/nome-sigla-portal/*`,
    getProgramasByExibidora: `${gandalfBFFHost}/public/tabelapreco/precos`,
    cadastroLongTailSIAM: `${gandalfBFFHost}/login/v5/oauth/create-user-long-tail`,
    getClientesByName: `${gandalfBFFHost}/cliente/getby_name`,
    getClientesByCpfCnpj: `${gandalfBFFHost}/cliente/getby_cpfcnpj`,
    getAgenciasByName: `${gandalfBFFHost}/agencia/nome`,
    getContatosAgenciaCliente:
      'https://api.prd.negocios.tvglobo.com.br/mobile/antecipa/notificacoes-oportunidades/buscar-contatos-agencia-cliente',
    enviarNotificacoesOportunidade:
      'https://api.prd.negocios.tvglobo.com.br/mobile/antecipa/notificacoes-oportunidades/enviar',
    getOportunidades:
      'https://api.prd.negocios.tvglobo.com.br/mobile/antecipa/notificacoes-oportunidades/buscar-por-agencia-cliente',
    uploadImagemAgencia: `${gandalfBFFHost}/painel-metricas/upload/file`,
    simulacaoById: `${gandalfBFFHost}/public/simulador-midia/simulacao`,
    getDisponibilidade: `${gandalfBFFHost}/public/simulador-midia/disponibilidade`,
    veicularPrograma: `${gandalfBFFHost}/public/simulador-midia/veiculacoes`,
    getDiasSemanaProgramas: `${gandalfBFFHost}/public/programa/api/v1/nome-sigla-portal/*`,
    comprarProgramas: `${gandalfBFFHost}/public/simulador-midia/compras`,
    consultarVeiculacoesSimulacao: `${gandalfBFFHost}/public/simulador-midia/disponibilidade/check/simulacao/{simulacaoId}`,
    getSugestaoTemplates: `${gandalfBFFHost}/public/video-integration/templates`,
    tiposNegocio: `${gandalfBFFHost}/public/video-integration/genres`,
    videoPreview: `${gandalfBFFHost}/public/video-integration/videorequests`,
    createAlbum: `${gandalfBFFHost}/public/video-integration/album`,
    uploadAlbumImage: `${gandalfBFFHost}/public/video-integration/album/upload`,
    comprarPedido: `${gandalfBFFHost}/public/simulador-midia/compras`,
    videoPrices: `${gandalfBFFHost}/public/simulador-midia/video_prices`,
    dadosUsuario: `${gandalfBFFHost}/public/simulador-midia/registro/contato`,
    suporte: `${gandalfBFFHost}/public/simulador-midia/suporte`,
    getMapasFalhados: `${gandalfBFFHost}/public/simulador-midia/compras/fail-list`,
    retentarMapaFalhado: `${gandalfBFFHost}/public/simulador-midia/compras/retry`,
    videoDraft: `${gandalfBFFHost}/public/video-integration/videodraft`,
    permissaoValidarVideo: `${gandalfBFFHost}/public/simulador-midia/simulacao/validate/videoapprovaltoken`,
    sendImagesByAlbumId: `${gandalfBFFHost}/public/video-integration/album/imageurl`,
    cupom: `${gandalfBFFHost}/public/simulador-midia/compras/{simulacaoId}/cupom/validar`,
    getSLAs: `${gandalfBFFHost}/public/simulador-midia/sla`,
    getSLASimulacao: `${gandalfBFFHost}/public/simulador-midia/simulacao/{simulacaoId}/verificar-sla`,
    opcoesParcelamento: `${gandalfBFFHost}/public/simulador-midia/compras/opcoes-parcelamento`,
    atualizarContato: `${gandalfBFFHost}/public/simulador-midia/registro/complemento`,
    getTransacoesPagamento: `${gandalfBFFHost}/dashboard-affiliates/recipients/{uuid}/customer-transactions?page={page}&pageSize={pageSize}`,
    estornoPagamento: `${gandalfBFFHost}/dashboard-affiliates/customer-transactions/{id}/refund`,
    getEstados: `${gandalfBFFHost}/public/simulador-midia/sua-globo/localizar/estados`,
    getCidadesByEstado: `${gandalfBFFHost}/public/simulador-midia/sua-globo/localizar/cidades?cdUf={cdUf}`,
    videoIntegration: `${gandalfBFFHost}/public/video-integration/videodraft`,
    autenticacao: `https://api.prd.negocios.tvglobo.com.br/auth/realms/globoads/tvg-token/token`,
    autenticacaoPosLogin: `https://iam.negocios.tvglobo.com.br/auth/realms/globoads/protocol/openid-connect/token`,
    tokenInfo: `${gandalfBFFHost}/login/v3/oauth/tokeninfo`,
    redefinirSenha: `${gandalfBFFHost}/login/redefinir-senha`,
    getTransacoesPagamentoHistorico: `${gandalfBFFHost}/dashboard-affiliates/customer-transactions/{id}/timeline`,
    reenviaEmailCadastro: `${gandalfBFFHost}/public/login/v3/oauth/send-verify-email`,
    getBalance: `${gandalfBFFHost}/dashboard-affiliates/recipients/{id}/balance`,
    getRecipients: `${gandalfBFFHost}/dashboard-affiliates/recipients`,
    getContasBancarias: `${gandalfBFFHost}/dashboard-affiliates/recipients/{id}/bankaccounts`,
    configuracaoRecebedora: `${gandalfBFFHost}/dashboard-affiliates/recipients/{id}`,
    limitesAntecipacao: `${gandalfBFFHost}/dashboard-affiliates/recipients/{id}/anticipations/limits`,
    antecipacao: `${gandalfBFFHost}/dashboard-affiliates/recipients/{id}/anticipations`,
    getAntecipacoes: `${gandalfBFFHost}/dashboard-affiliates/recipients/{id}/anticipations?page={page}&count={count}`,
    detalhesTransacao: `${gandalfBFFHost}/dashboard-affiliates/customer-transactions/transaction/{id}`,
    reenviaSMSCadastro: `${gandalfBFFHost}/login/v5/oauth/create-user-verification-code`,
    validarCodigoSMS: `${gandalfBFFHost}/login/v5/oauth/confirm-verification-code`,
    alterarTelefone: `${gandalfBFFHost}/public/simulador-midia/registro/contato/telefone-sms`,

    getExibidoras: `${gandalfBFFHost}/public/video-integration/videorequests/exhibitors`,
    getNomesEmpresas: `${gandalfBFFHost}/public/video-integration/videorequests/name`,

    videoPreviewProdutora: `${gandalfBFFHost}/public/player-integration/local/videos`,
    getFreePurchaseCount: `${gandalfBFFHost}/public/player-integration/local/videos/free-purchase-count`,
    purchaseConfirmation: `${gandalfBFFHost}/public/player-integration/purchaseconfirmation`,
    updateVideoStatusProdutora: `${gandalfBFFHost}/public/video-integration/controle-gmid/player`,

    videoPreviewByUser: `${gandalfBFFHost}/public/video-integration/videorequests/by-user`,
    atributosProdutosServicos: `${gandalfBFFHost}/public/video-integration/product_service`,
    gerarRecomendacao: `${gandalfBFFHost}/public/simulador-midia/simulacao/{simulacaoId}/advertisement`,
    filtrarRecomendacao: `${gandalfBFFHost}/public/simulador-midia/simulacao/{simulacaoId}/advertisement/filter`,
    parametroUsuario: `${gandalfBFFHost}/public/simulador-midia/parametro-usuario`,

    autorizarAgencia: `${gandalfBFFHost}/public/simulador-midia/registro/credenciamentoAgencia`,
    getAgencias: `${gandalfBFFHost}/public/simulador-midia/registro/credenciamentoAgencia`,
    getCartaCredenciamentoPDF: `${gandalfBFFHost}/public/simulador-midia/pdf/carta-credenciamento-agencia`,

    customer: `${gandalfBFFHost}/globo-pagamentos-api/api/v1/customers`,
    envioSMSVideo: `${gandalfBFFHost}/public/simulador-midia/contato/notificacao`,

    comprarBoleto: `${gandalfBFFHost}/public/simulador-midia/compras/{simulacaoId}/boleto`,

    simulacaoFluxoCompra: `${gandalfBFFHost}/public/simulador-midia/simulacao/{simulacaoId}/fluxocompra`,
    leads: `${gandalfBFFHost}/public/simulador-midia/leads`,
    tabelaPreco: `${gandalfBFFHost}/public/tabelapreco`,

    comprarPix: `${gandalfBFFHost}/public/simulador-midia/compras/{simulacaoId}/pix`,
    adicionarInformacoesVideo: `${gandalfBFFHost}/public/simulador-midia/simulacao/{simulacaoId}/add-informacao-contrucao-video`,

    criarNovoLead: `${gandalfBFFHost}/public/simulador-midia/leads`,
    regerarProgramas: `${gandalfBFFHost}/public/simulador-midia/v2/simulacao/{simulacaoId}/programas/regerar`,
    simulacaoV2: `${gandalfBFFHost}/public/simulador-midia/v2/simulacao/{simulacaoId}`,

    obterImpactoProgramas: `${gandalfBFFHost}/public/simulador-midia/programas`,
    videoPreviewV2: `${gandalfBFFHost}/public/video-integration/v2/videorequests`,
    getProgramasByExibidoraV2: `${gandalfBFFHost}/public/tabelapreco/v2/precos/exibidoras`,

    verificarCondecinePorExibidora: `${gandalfBFFHost}/public/simulador-midia/sua-globo/exibidora`,
    verificarPrimeiraCompra: `${gandalfBFFHost}/public/simulador-midia/compras/cupom/primeira-compra`,
    obterSugestaoData: `${gandalfBFFHost}/public/simulador-midia/disponibilidade/simulacao/{idSimulacao}/sugestao-de-datas`,

    fotografiaAgendamentos: `https://api.prd.negocios.tvglobo.com.br/portal/fotografia/agendamentos`,
    fotografiaPreco: `https://api.prd.negocios.tvglobo.com.br/fotografia/price`,
    simulacaoFotografia: `${gandalfBFFHost}/public/simulador-midia/simulacao/{simulacaoId}/fotografia`,
    exibidoraServicoFotografia: `https://api.prd.negocios.tvglobo.com.br/fotografia/mission/exhibitor/validate?meExibidora={ME_EXIBIDORA}`,

    alterarExibidoras: `${gandalfBFFHost}/public/simulador-midia/v3/simulacao/{simulacaoId}/exibidoras`,

    sugestaoMultiexibidora: `${gandalfBFFHost}/public/simulador-midia/v2/leads/sugestao`,
    simulacaoMultiexibidora: `${gandalfBFFHost}/public/simulador-midia/v3/simulacao/{simulacaoId}`,
    leadsV2: `${gandalfBFFHost}/public/simulador-midia/v2/leads`,
    gerarRecomendacaoV2: `${gandalfBFFHost}/public/simulador-midia/v3/simulacao/{simulacaoId}/advertisement`,
    filtrarRecomendacaoV2: `${gandalfBFFHost}/public/simulador-midia/v3/simulacao/{simulacaoId}/advertisement/filter`,

    resetPassword: `${gandalfBFFHost}/login/v5/oauth/update-password`,
    alterarExibidorasLead: `${gandalfBFFHost}/public/simulador-midia/v2/leads/{leadId}/exibidoras`,

    obterImpactoProgramasV2: `${gandalfBFFHost}/public/simulador-midia/V2/programas`,
    envioVati: `${gandalfBFFHost}/public/simulador-midia/player-video/vati/solicitar-registro-video`,

    pacotesNovoAlgoritimo: `${gandalfBFFHost}/public/simulador-midia/pacotes`,
    criarSimulacaoAlgoritimo: `${gandalfBFFHost}/public/simulador-midia/v4/simulacao`,
    recuperarSimulacaoId: `${gandalfBFFHost}/public/simulador-midia/v4/simulacao/{simulacaoId}`,
    alterarSimulacaoId: `${gandalfBFFHost}/public/simulador-midia/v4/simulacao/{simulacaoId}`,
    tipoExperiencia: `${gandalfBFFHost}/public/simulador-midia/v4/simulacao/tipo-simulacao`,
    getInvestimentoExibidora: `${gandalfBFFHost}/public/simulador-midia/v2/simulacao/preview`,
    postLeads: `${gandalfBFFHost}/public/simulador-midia/v2/leads`,
    postLeadsAlgoritmo: `${gandalfBFFHost}/public/simulador-midia/v4/simulacao/leads`,
    lpVantagens: `${gandalfBFFHost}/public/simulador-midia/vantagens`,
    lpBanners: `${gandalfBFFHost}/public/simulador-midia/banners`,
    lpDepoimentos: `${gandalfBFFHost}/public/simulador-midia/depoimentos`,
    politicaDesconto: `${gandalfBFFHost}/public/simulador-midia/politica-desconto`,

    exibidoraKantar: `${gandalfBFFHost}/public/simulador-midia/pos-venda/exibidora-kantar/{exibidora}`,
    campanhas: `${gandalfBFFHost}/public/simulador-midia/pos-venda/campanhas`,
    dadosCabecalho: `${gandalfBFFHost}/public/simulador-midia/pos-venda/campanhas/{campanha}`,
    ultimaCampanhaFinalizada: `${gandalfBFFHost}/public/simulador-midia/pos-venda/ultima-campanha-finalizada`,
    dadosGrafico: `${gandalfBFFHost}/public/posvenda/tv-aberta/campanhas/{campanha}/impacto`,
    dadosAlcanceFrequencia: `${gandalfBFFHost}/public/posvenda/tv-aberta/campanhas/{campanha}/frequencia-alcance`,
    editarNomeCampanha: `${gandalfBFFHost}/public/simulador-midia/v4/simulacao/atualiza-nome`,
    proximaVeiculacao: `${gandalfBFFHost}/public/simulador-midia/pos-venda/campanhas/proxima-veiculacao/{campanha}`,
    // APIs digital
    preSignUrl: `${gandalfBFFHost}/public/creative-sender/upload/pre-sign-url/{nome_do_arquivo}?contentType={tipo_do_arquivo}`,
    digitalFormats: `${gandalfBFFHost}/public/digital-api/formatos`,
    uploadDigitalImage: `${gandalfBFFHost}/public/creative-sender/criativos/upload/base-64`,
    gerarCampanhaDigital: `${gandalfBFFHost}/public/digital-api/campanhas`,
    atualizarCampanhaDigital: `${gandalfBFFHost}/digital-api/campanhas/{idCampanha}`,
    envioCriativo: `${gandalfBFFHost}/public/digital-api/criativos`,
    associarCriativoNaCampanha: `${gandalfBFFHost}/public/digital-api/criativos/associar-campanha`,
    recuperarCampanhaDigitalId: `${gandalfBFFHost}/digital-api/campanhas/{idCampanha}`,

    deletarCriativo: `${gandalfBFFHost}/digital-api/campanhas/remove-criativo/{idCampanha}/{idCriativo}`,
    getPreviewImagemCoordenadas: `${gandalfBFFHost}/public/digital-api/criativo-preview?formato={formato}&mnemonico={mnemonico}&plataforma={plataforma}`,
    getDigitalCategoryMetrics: `https://api-pulso.g.globo/globopulso/campaigns/digital/metrics/category`,
    getDigitalImpressions: `https://api-pulso.g.globo/globopulso/campaigns/digital/metrics/impressions`,
    getDigitalIndicators: `https://api-pulso.g.globo/globopulso/campaigns/digital/metrics/indicators`,
    getDigitalCreatives: `https://api-pulso.g.globo/globopulso/campaigns/digital/creatives`,
    getDigitalCampaignSummary: `${gandalfBFFHost}/public/digital-api/campanhas`,

    listaFaixasEtarias: `${gandalfBFFHost}/public/digital-api/target/faixas-etarias`,
    listaInteresses: `${gandalfBFFHost}/public/digital-api/target/interesses`,
    listaSexos: `${gandalfBFFHost}/public/digital-api/target/sexos`,
    listaCidades: `${gandalfBFFHost}/public/digital-api/target/localidades`,
    publicoEstimado: `${gandalfBFFHost}/public/digital-api/target/publico`,
    goals: `${gandalfBFFHost}/public/digital-api/objetivos`,
    atualizarLinkCampanha: `${gandalfBFFHost}/digital-api/campanhas/atualiza-link/{idCampanha}`,
    avaliarUrlCampanhaOpec: `${gandalfBFFHost}/public/digital-api/avaliacao-url-campanha`,
    impressoesImpactos: `${gandalfBFFHost}/public/digital-api/target/impactos`,
    getCampaignStatusByCategory: `${gandalfBFFHost}/digital-api/campanhas/status/{categoria}`,
    saveSimulation: `${gandalfBFFHost}/public/simulador-midia/v2/leads`,
    saveSimulationV4: `${gandalfBFFHost}/public/simulador-midia/v4/simulacao`,
    pagarDigital: `${gandalfBFFHost}/digital-api/campanhas/{idCampanha}/pagar`,
    getCampaignByOrder: `https://api-pulso.g.globo/globopulso/campaigns/digital/{salesOrderId}`,
    getDigitalFeed: `${gandalfBFFHost}/digital-api/campanhas/feed`,
    getDigitalFeedPorCriativos: `${gandalfBFFHost}/digital-api/campanhas/feed/{id}/criativos`,
    removeFeedNotificacao: `${gandalfBFFHost}/digital-api/campanhas/feed/dismiss/{id}`,
    getCPM: `${gandalfBFFHost}/public/digital-api/cpm`,
    getCupom: `${gandalfBFFHost}/digital-api/campanhas/{idCampanha}/cupom`,
    cupomDesconto: `${gandalfBFFHost}/digital-api/campanhas/{idCampanha}/cupom/{codCupom}`,
    imageCompress: `${gandalfBFFHost}/public/image-resizer/upload`,
    saveDigitalUrl: `${gandalfBFFHost}/digital-api/campanhas/{idCampanha}/url`,
    pauseCreative: `${gandalfBFFHost}/public/digital-api/criativos/desativar`,
    activateCreative: `${gandalfBFFHost}/public/digital-api/criativos/ativar`,
    estenderCampanha: `${gandalfBFFHost}/digital-api/campanhas/{idCampanha}/estender`,
    uploadNameVideo: `${gandalfBFFHost}/public/simulador-midia/v3/simulacao/{simulacaoId}/update-name-video`,

    firstAccess: `${gandalfBFFHost}/public/simulador-midia/video-upload/primeiro-acesso`,
    videoUploaderName: `${gandalfBFFHost}/video-uploader/v1/media-files/`,
    excluirSimulacaoTV: `${gandalfBFFHost}/public/simulador-midia/simulacao/{simulacaoId}/inativar`,
    videoUploaderLambda: `${gandalfBFFHost}/video-uploader/v1/media-files`,
    registerVideoPost: `${gandalfBFFHost}/public/player-integration/videos/metadata`,
    registerVideoInfo: `${gandalfBFFHost}/public/player-integration/videos/metadata/{idVideo}`,
    videoRefuse: `${gandalfBFFHost}/public/player-integration/videos/`,
    getPaymentFees: `${gandalfBFFHost}/public/player-video-integration/video-integration/preco-padrao/{videoType}`,
    dataTablePosVenda: `${gandalfBFFHost}/public/digital-api/criativos/`,
    s3UrlPath: `https://storage.googleapis.com/video-uploader-prod/`,
    vaultBaseApi:
      'https://s3.glbimg.com/v1/AUTH_24e9b0b1475c40edb8c000a80d7e0722/PROD',

    // Novos endpoints para a tela de alcance
    impactsByValue: `${gandalfBFFHost}/public/digital-api/target/impressoes/impactos`,
    impactsBySlider: `${gandalfBFFHost}/public/digital-api/target/usuarios/impactos`,
    // Final novos endpoints para a tela de alcance
    getProducerByUser: `${gandalfBFFHost}/public/simulador-midia/sua-globo/tipo-produtora`,
    postDataAndRedirectAcelerai: `${gandalfBFFHost}/public/player-video-integration/video-integration/create-video-request`,
    getVideosVatiByUser: `${gandalfBFFHost}/player-video-integration/video-integration/by-user/{username}`,
    videoVatiPreview: `${gandalfBFFHost}/public/player-video-integration/video-integration/videorequests/{id}`,
    getMyOrdersUrlAcelerai: `${gandalfBFFHost}/public/player-video-integration/video-integration/get-my-orders-url`,
    checkUserIsMaster: `${gandalfBFFHost}/onboard/invitation/check`,
    getIncarceratedClient: `${gandalfBFFHost}/public/digital-api/clientes/encarteirado`,
    getInvites: `${gandalfBFFHost}/onboard/invitation/{id}`,
    deleteInvites: `${gandalfBFFHost}/onboard/invitation/{id}`,
    createInviteAuthorization: `${gandalfBFFHost}/onboard/invitation/request-owner-authorization`,
    createInviteGuest: `${gandalfBFFHost}/onboard/invitation/guest-request`,
    updateInvites: `${gandalfBFFHost}/onboard/invitation`,
    resendInvites: `${gandalfBFFHost}/onboard/invitation/resend/{id}`,
    postPaymentTelephone: `${gandalfBFFHost}/onboard/users/{username}/phones`,
    usersInfo: `${gandalfBFFHost}/onboard/users/{username}/info`,
    putFluxoCompra: `${gandalfBFFHost}/public/simulador-midia/simulacao/{simulacaoId}/altera-fluxocompra`,
    getListClients: `${gandalfBFFHost}/onboard/users/{username}/companies`,
    materialManager: `${gandalfBFFHost}/public/video-manager/materias`,
    getExbList: `${gandalfBFFHost}/public/video-manager/materias/{transaction-id}/abrangencia`,
    getPaymentInfo: `${gandalfBFFHost}/public/video-manager/materias/{transaction-id}`,
    getMaterialExhibitorDetail: `${gandalfBFFHost}/public/simulador-midia/sua-globo/exhibitorcode`,
    postDataForPartnerBring: `${gandalfBFFHost}/public/player-video-integration/video-integration/notify-partner`,
    qrcodeBiometria: `${gandalfBFFHost}/onboard/antifraud/biometry-link`,
    cpfValidation: `${gandalfBFFHost}/onboard/antifraud/validacao/cpf`,
    postponeBiometry: `${gandalfBFFHost}/onboard/antifraud/{username}/postpone-biometry`,
    updatePhoneNumber: `${gandalfBFFHost}/onboard/users/{username}/phones/{phoneId}`,
    getPositionsList: `${gandalfBFFHost}/onboard/functions`,
    registerPosition: `${gandalfBFFHost}/onboard/user-company`,
    qsaValidation: `${gandalfBFFHost}/onboard/antifraud/validate/qsa`,
    updateUser: `${gandalfBFFHost}/onboard/users/{username}/`,
    updateFirstAccess: `${gandalfBFFHost}/onboard/users/{username}/first-access`,
    updateNameAnunTv: `${gandalfBFFHost}/public/simulador-midia/v4/simulacao`,
    pagarPlanoMidia: `${gandalfBFFHost}/public/simulador-midia/compras/{simulacaoId}/pagar-midia`,
    associarVideo: `${gandalfBFFHost}/public/simulador-midia/associar/{simulacaoId}`,
    inviteByUsername: `${gandalfBFFHost}/onboard/invitation/user/{username}`,
    getInvitationStatusByCompany: `${gandalfBFFHost}/onboard/invitation/invitation-userdata-company/{companyId}`,
    getReceiptList: `${gandalfBFFHost}/financeiro/faturas/paged`,
    getReceiptLink: `${gandalfBFFHost}/financeiro/faturas/nfe`,
    idSendEmailControl: `${gandalfBFFHost}/public/digital-api/criativos/controle-notificacao/desativar`,
    validateCrt: `${gandalfBFFHost}/public/player-integration/videos/metadata/crt-valid`,
    createMidiaPlan: `${gandalfBFFHost}/public/simulador-midia/planomidia`,
    updateMaterialManager: `${gandalfBFFHost}/public/video-manager/materias/{transaction-id}`,
    formularionSuporteEnvio: `${gandalfBFFHost}/public/simulador-midia/suporte`,
    cuponWasUsedBlackFriday: `${gandalfBFFHost}/public/simulador-midia/compras/cupom/cnpj-usou`,
    changeCreativeWasReproved: `${gandalfBFFHost}/digital-api/campanhas/{idCampanha}/substitui-criativo/{idCriativoSubstituido}/{idCriativoNovo}`,
    uploadSupportImage: `https://support-contact-form.g.globo/upload`,
    sendSupportCall: `https://support-contact-form.g.globo/form-data`,
    saqueAfiliadas: `${gandalfBFFHost}/dashboard-affiliates/recipients/withdraw`,
    getPacotesAuto: `${gandalfBFFHost}/public/simulador-midia/v2/pacotes`,
    getOiCliente: `${gandalfBFFHost}/clients/api/v1/clientes/clientes`,
    deleteDigitalDraft: `${gandalfBFFHost}/digital-api/campanhas/{idCampanha}`,
    estornarDigital: `${gandalfBFFHost}/digital-api/campanhas/{idCampanha}/estornar`,
    checkClientCredit: `${gandalfBFFHost}/public/simulador-midia/compras/{idCampanha}/15dfm`,
    checkClientCreditDigital: `${gandalfBFFHost}/digital-api/campanhas/15dfm`,
    uploadVideoDigitalPreSignd: `${gandalfBFFHost}/public/creative-sender/v2/creatives`,
    uploadVideoDigitalValidation: `${gandalfBFFHost}/public/creative-sender/v2/creatives/zip/content`,
    getCreatives: `${gandalfBFFHost}/public/creative-sender/v2/creatives/paginated`,
    deleteCreatives: `${gandalfBFFHost}/public/creative-sender/v2/creatives/delete`,
    createdZipToCreative: `${gandalfBFFHost}/public/creative-sender/v2/creatives/zip`,
    getZipCreative: `${gandalfBFFHost}/public/creative-sender/v2/creatives/zip/content/{externalID}`,
    getPagarMeKey: `${gandalfBFFHost}/public/globo-pagamentos-api/api/v1/credit-cards/key`,
    getCreativeStatusHistory: `${gandalfBFFHost}/public/creative-sender/v2/creatives/{externalId}/events`,
    getCreativeStatusHistoryFromExternal: `${gandalfBFFHost}/public/creative-sender/v2/creatives/external/{externalId}/events`,
    getCreativeUsageHistory: `${gandalfBFFHost}/public/digital-api/criativos/campanhas-associadas/{idCriativo}`,
    getGeneratedText:
      'https://gandalf.globo.com/adcore/v1/adstudio/generate-ads-texts',
    getGeneratedImage:
      'https://gandalf.globo.com/adcore/v1/adstudio/generate-ads-images',
    postGenerateCreative:
      'https://gandalf.globo.com/adcore/v1/adstudio/generate-creative',
    postSaveFile: 'https://gandalf.globo.com/adcore/v1/adstudio/upload-file',
    getRemoteConfigs:
      'https://gandalf.globo.com/{tenant}/v1/remote-configs/group/{group}/scope/{scope}',

    adstudio: {
      getGeneratedText: adstudioBase + '/generate-ads-texts',
      getGeneratedImage: adstudioBase + '/generate-ads-images',
      baseAssets: adstudioBase + '/assets',
      listAssets: adstudioBase + '/assets?size={size}&page={page}&sort={sort}',
      getAsset: adstudioBase + '/assets/{idAsset}',
      favoriteAsset: adstudioBase + '/assets/{idAsset}/favorite',
    },
  },

  debugApiResponse: false,
  showExplicitError: false,
  analyticsActivated: true,
  menus: {
    mostrarUpload: true,
    mostrarCarrinho: false,
    mostrarMapas: true,
    mostrarDashboard: true,
    mostrarTrocaTitulo: true,
    mostrarRetentativa: true,
    mostrarConsultaDisponibilidade: true,
    mostrarCompraViaTela: true,
    mostrarPainelMetricas: true,
    mostrarEnvioOportunidades: true,
  },
  urls: {
    creativeManager: 'https://creativemanager.negocios8.redeglobo.com.br',
    profissionaisDoAno: 'http://profissionaisdoano.redeglobo.com.br',
    tvGloboMidia: 'https://emidia.tvglobo.com.br/#',
    siscom: 'https://siscom.redeglobo.com.br',
    negocios8: '',
    portal: 'https://globoads.globo.com/',
    esqueciSenha: 'https://globoads.globo.com/onboarding/#/forgot-password/',
    simulador: 'https://pme.globoads.globo.com',
    landingPage: 'https://globoads.globo.com/pme-anuncie-na-globo',
    onboardingGloboADS: 'https://accounts.globoads.globo.com',
    novoLoginGloboSim: 'https://globoads.globo.com/login/',
    globoads: 'https://globoads.globo.com',
    qrcodegen: 'https://api.prd.negocios.tvglobo.com.br/qrcode-tool/',
    termos_url: 'https://termos.globoads.globo/',
    lp: 'https://globoads.globo.com/',
  },
  pagarme: {
    encryptionKey: window['PAGAR_ME_ENCRYPTION_KEY'],
  },
  webchat: {
    deploymentId: '572Hu000000V9BW',
    organizationId: '00D1N000001hq0u',
    deploymentUrl:
      'https://c.la3-c2-ia7.salesforceliveagent.com/content/g/js/60.0/deployment.js',
    apiEndpointUrl: ' https://d.la3-c2-ia7.salesforceliveagent.com/chat',
  },
  keycloak: {
    script: 'https://iam.negocios.tvglobo.com.br/auth/js/keycloak.js',
    auth: 'https://iam.negocios.tvglobo.com.br/auth',
  },
  keyName: 'gcpKey',
  access: {
    iconLibrary: false,
  },
  hostUrls: ['https://pme.globoads.globo.com/'],
}
