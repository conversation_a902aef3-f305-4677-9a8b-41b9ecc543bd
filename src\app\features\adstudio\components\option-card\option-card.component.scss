.selected-card {
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: -2px;
    right: -2px;
    bottom: -2px;
    left: -2px;
    background: linear-gradient(45deg, #4f85e8, #3c6ce3, #2e52e0, #5670e3);
    border-radius: 0.75rem;
    z-index: -1;
  }
}

.icon-gradient {
  background: linear-gradient(
    135deg,
    rgba(0, 121, 253, 1) 0%,
    rgba(45, 81, 251, 1) 33%,
    rgba(90, 41, 250, 1) 66%,
    rgba(136, 0, 248, 1) 100%
  );
}

p {
  font-family: var(--Font-Family-Book, 'Inter Variable');
}
