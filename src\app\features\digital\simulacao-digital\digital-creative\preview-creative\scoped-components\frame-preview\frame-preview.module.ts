import { SharedModule } from '@shared/shared.module'
import { NgModule } from '@angular/core'
import { CommonModule } from '@angular/common'
import { FramePreviewComponent } from './frame-preview.component'
import { PreviewImageStructureModule } from '../preview-image-structure/preview-image-structure.module'
import { SkeletonPreviewFakeModule } from '../skeleton-preview-fake/skeleton-preview-fake.module'
import { PreviewImagePublicityModule } from '../../preview-image-publicity/preview-image-publicity.modules'
import { PreviewImageCarrosselModule } from '../../preview-image-carrossel/preview-image-carrossel.module'
import { GloboPagesPreviewDesktopModule } from '../globo-pages-preview-desktop/globo-pages-preview-desktop.module'
import { GloboPagesPreviewMobileModule } from '../globo-pages-preview-mobile/globo-pages-preview-mobile'

@NgModule({
  declarations: [FramePreviewComponent],
  imports: [
    CommonModule,
    SharedModule,
    GloboPagesPreviewDesktopModule,
    GloboPagesPreviewMobileModule,
    PreviewImageStructureModule,
    PreviewImagePublicityModule,
    SkeletonPreviewFakeModule,
    SkeletonPreviewFakeModule,
    PreviewImageCarrosselModule,
  ],
  exports: [FramePreviewComponent],
})
export class FramePreviewModule {}
