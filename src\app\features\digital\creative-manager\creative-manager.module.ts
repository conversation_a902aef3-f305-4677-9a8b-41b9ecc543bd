import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core'
import { CommonModule } from '@angular/common'
import { CreativeManagerComponent } from './creative-manager.component'
import { SharedModule } from '@app/shared/shared.module'
import '@adtech/ui-kit/webcomponents'
import { MatTabsModule } from '@angular/material/tabs'
import { AddedComponent } from './crative-status/added/added.component'
import { CreativeCardComponent } from './components/creative-card/creative-card.component'
import { CreativeInfoComponent } from './components/creative-info/creative-info.component'
import { DialogDeleteCreative } from './components/dialog-delete-creative/dialog-delete-creative.component'
import { ValidationComponent } from './crative-status/validation/validation.component'
import { FailedComponent } from './crative-status/failed/failed.component'
import { ApprovedComponent } from './crative-status/approved/approved.component'
import { GradientButtonComponent } from './components/gradient-button/gradient-button.component'
import { NoContentComponent } from './components/no-content/no-content.component'
import { CreativeTimelineComponent } from './components/creative-timeline/creative-timeline.component'
import { DialogPauseActivatedCreativeComponent } from './components/dialog-pause-activated-creative/dialog-pause-activated-creative.component'
import { AdstudioModule } from '@app/features/adstudio/adstudio.module'
import { AssetsImagesListComponent } from './components/assets-images-list/assets-images-list.component'
import { AssetsCardComponent } from './components/assets-card/assets-card.component'
import { AsstesInfoComponent } from './components/asstes-info/asstes-info.component'
import { DialogGenericAssetsComponent } from './dialogs/assets/dialog-generic-assets/dialog-generic-assets.component'
import { DialogAssetsListComponent } from './dialogs/assets/dialog-assets-list/dialog-assets-list.component'
import { SkeletonLoaderComponent } from './components/skeleton-loader/skeleton-loader.component'
import { DateConvertPipe } from './pipes/date-convert.pipe'
import { InfoBarComponent } from './components/info-bar/info-bar.component'

@NgModule({
  imports: [CommonModule, SharedModule, MatTabsModule, AdstudioModule],
  declarations: [
    CreativeManagerComponent,
    AddedComponent,
    CreativeCardComponent,
    CreativeInfoComponent,
    ValidationComponent,
    DialogDeleteCreative,
    FailedComponent,
    ApprovedComponent,
    GradientButtonComponent,
    NoContentComponent,
    CreativeTimelineComponent,
    DialogPauseActivatedCreativeComponent,
    AssetsImagesListComponent,
    AssetsCardComponent,
    AsstesInfoComponent,
    DialogGenericAssetsComponent,
    DialogAssetsListComponent,
    SkeletonLoaderComponent,
    DateConvertPipe,
    InfoBarComponent,
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  exports: [
    CreativeCardComponent,
    CreativeInfoComponent,
    AssetsImagesListComponent,
  ],
})
export class CreativeManagerModule {}
