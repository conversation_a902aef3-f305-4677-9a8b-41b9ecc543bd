import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>t, ViewChild } from '@angular/core'
import { Subscription } from 'rxjs'
import { MatDialogConfig } from '@angular/material/dialog'
import { BreakpointObserver, Breakpoints } from '@angular/cdk/layout'
import { first } from 'rxjs/operators'
import { ImageService } from './services/image.service'
import { Router } from '@angular/router'
import { MatLegacyDialog as MatDialog } from '@angular/material/legacy-dialog'
// eslint-disable-next-line max-len
import { DialogEscolhaEnvioCriativo } from '../simulacao-digital/digital-creative/components/dialogs/dialog-escolha-envio-criativo/dialog-escolha-envio-criativo.component'
import {
  RemoteConfigs,
  RemoteConfigsService,
} from '@app/core/services/remote-configs/remote-configs.service'
import { GeradorCriativosService } from '@app/features/adstudio/services/gerador-criativos.service'
import {
  DigitalCreativeDetailsResponse,
  CreativeDetails,
} from '@app/shared/models/creative-details'
import { TechnicalValidationCreativeStatus } from '@app/shared/models/status-criativo-digital'
import { ERROR_DIALOG_HTML } from '@app/shared/utils/swal-templates'
import { DigitalActions } from '@app/store/digital/digital.actions'
import { PreviewType } from '../simulacao-digital/digital-creative/preview-creative/data-model/data-model-consts'
import { SendCreativeService } from '@app/features/adstudio/services/send-creative.service'
import { CreativeService } from '@app/core/services/creative-service/creative-service.service'
import { NotificationService } from '@app/core/services/notification/notification.service'
import { DigitalService } from '@app/core/services/no-auth/digital/digital.service'
import { DigitalSenderHelper } from '@app/shared/utils/digital-sender-helper'
import { SendFilesComponent } from '../simulacao-digital/digital-creative/components/send-files/send-files.component'
import { AppState } from '@app/store/app.model'
import { Store } from '@ngrx/store'
import { InfoStateService } from './components/creative-info/info-state.service'
import { AssetsItemService } from './services/assets-item.service'
import { AssetsItem } from '@app/core/services/no-auth/digital/model/digital-interface-utils'
import { AddedComponent } from './crative-status/added/added.component'
import { formatTypes } from './utils/dictionary'
import { v4 as uuidv4 } from 'uuid'
import { environment } from '@env/environment'
import { AssetDrawerService } from './services/asset-drawer.service'

@Component({
  selector: 'app-creative-manager',
  templateUrl: './creative-manager.component.html',
})
export class CreativeManagerComponent implements OnInit, OnDestroy {
  tabs = [{ label: 'Tab 1' }, { label: 'Tab 2' }, { label: 'Tab 3' }]
  drawerOpen = false
  drawerAssetsOpen = false
  subs = new Subscription()
  dialogConfig = new MatDialogConfig()
  settings: RemoteConfigs
  isSidebarOpen = false
  detailsImageStorage: File
  typeImage: string
  imageUrl: string
  messageError: string
  statusCriativo: boolean
  typeMsg: number
  iconIAEditSrc = `${environment.api.vaultBaseApi}/icons/ai-create-gradient.svg`
  private isUploading: boolean = false

  @ViewChild(SendFilesComponent) sendFilesComponent: SendFilesComponent
  @ViewChild(AddedComponent) addedComponente: AddedComponent
  page = 0
  size = 4
  format: any
  managementFile: any
  isGalleryVersion: any
  imageHeigth: number
  imageWidth: number
  dialogHelper: any
  loopObject = {}
  originAdsStudio: boolean = false

  constructor(
    private assetDrawerService: AssetDrawerService,
    private breakpointObserver: BreakpointObserver,
    private imageService: ImageService,
    private router: Router,
    private geradorCriativoService: GeradorCriativosService,
    private remoteConfigService: RemoteConfigsService,
    private dialog: MatDialog,
    private creativeIaService: SendCreativeService,
    private creativeService: CreativeService,
    private notificationService: NotificationService,
    private digitalService: DigitalService,
    private digitalSenderHelper: DigitalSenderHelper,
    private infoStateService: InfoStateService,
    protected store: Store<AppState>,
    private assetsItemService: AssetsItemService,
  ) {}

  ngOnInit() {
    this.initToggleObs()
    this.watchBreakpointsAndSetConfig()
    this.getConfigRemote('globosim', 'WEB', 'GENERAL-VIEW')
    this.creativeIaService.uploadResult$.subscribe(result => {
      if (result && result.file) {
        this.uploadIaCreative(result.file)
      }
    })
  }

  ngOnDestroy(): void {
    this.subs.unsubscribe()
    this.assetDrawerService.closeAssetModal()
  }

  initToggleObs() {
    this.subs.add(
      this.assetDrawerService.assetModalState$.subscribe(({ open, type }) => {
        if (type === 'asset') {
          this.drawerAssetsOpen = open
          this.drawerOpen = false
        } else if (type === 'creative') {
          this.drawerOpen = open
          this.drawerAssetsOpen = false
        } else {
          this.drawerOpen = false
          this.drawerAssetsOpen = false
        }
      }),
    )
  }

  onSidebarClose(isOpen: boolean) {
    this.isSidebarOpen = isOpen
  }
  watchBreakpointsAndSetConfig() {
    this.breakpointObserver
      .observe([Breakpoints.Handset])
      .pipe(first())
      .subscribe(result => {
        if (result.matches) {
          this.dialogConfig.position = {
            bottom: '0',
            left: '0',
            right: '0',
          }
          this.dialogConfig.width = '100%'
        } else {
          this.dialogConfig.width = '540px'
        }

        this.dialogConfig.disableClose = true
        this.dialogConfig.autoFocus = false
        this.dialogConfig.panelClass = 'custom-crop-dialog'
        this.dialogConfig.minHeight = '364px'

        this.imageService.dialogConfig = this.dialogConfig
      })
  }

  redirectAddCriativo() {
    if (this.settings?.SHOW_BUTTON_INIT_GERADOR) {
      this.openModalEscolha()
    } else {
      this.router.navigate([`digital/galeria/envio`])
    }
  }
  openModalEscolha(): void {
    this.dialog
      .open(DialogEscolhaEnvioCriativo)
      .afterClosed()
      .subscribe((response: number) => {
        if (response === 1) {
          window.open('digital/galeria/envio', '_blank')
        } else {
          this.openGeradorIA()
        }
      })
  }
  getConfigRemote(tenant, group, scope) {
    this.remoteConfigService
      .getConfigsRemote(tenant, group, scope)
      .subscribe(data => {
        this.settings = data
      })
  }

  // adição da parte de envio de criativo para o gerador

  sendFileS3(file: File) {
    this.sendFileS3AndValidate(file)
  }
  async sendFileS3AndValidate(file: File) {
    this.detailsImageStorage = file
    try {
      if (file.type.startsWith('image/')) {
        this.typeImage = 'image'
        this.creativeService.addOrUpdateCreativeDetails([
          new DigitalCreativeDetailsResponse({
            id: Math.random(),
            nome: file.name,
            subTipo: 'imagem',
            observacoes: 'loading',
          }),
        ])
        this.digitalService.sendFileToCompress(file).subscribe(
          response => {
            if (response) {
              this.imageUrl = response.imageUrl
              this.sendCriativo(this.imageUrl, file)
              return response
            }
          },
          () => {
            this.openDialogErrorGeneric()
          },
        )
        this.getDetailImage(file)
      } else {
        this.notificationService.showToastWarn(
          'O tipo de arquivo ' + file.type + ' não é permitido',
        )
      }
    } catch (error) {
      console.error('Erro durante o upload:', error)
      this.notificationService.showToastWarn(
        'Ocorreu um erro durante o upload do arquivo',
      )
    } finally {
      this.isUploading = false
    }
  }

  async sendCriativo(event, url) {
    const externalId = uuidv4()
    const envioCreative = await this.digitalSenderHelper
      .sendCreative(event, url, '', externalId, this.originAdsStudio)
      .then(response => {
        if (response.id) {
          this.creativeService.setIdsPending(response.id)
          return response
        }
        this.handleError()
      })

    this.getCreativeDetailsStandart(envioCreative.id)
  }
  getCreativeImage(creativeId) {
    this.digitalService
      .getCreativeDetails(creativeId)
      .pipe(first())
      .subscribe(
        (r: CreativeDetails) => {
          const status =
            r.criativoStatusHistoricoDTO[
              r.criativoStatusHistoricoDTO.length - 1
            ].status
          if (status !== TechnicalValidationCreativeStatus.PENDING) {
            this.setChangeCreativeDataAndValidate(r, status, creativeId)
          } else {
            this.loopObject[creativeId] = true
            this.creativeService.startCreativeLoop(
              (res, status, creativeId) => {
                this.setChangeCreativeDataAndValidate(res, status, creativeId)
              },
            )
          }
        },
        () => {
          this.openDialogErrorGeneric()
        },
      )
  }
  setChangeCreativeDataAndValidate(
    detalheCriativo: CreativeDetails,
    status: string,
    idCriativo,
  ) {
    this.messageError =
      detalheCriativo.erros && detalheCriativo.erros.length > 0
        ? detalheCriativo.erros[0]
        : null
    this.store.dispatch(
      DigitalActions.changeValue('listCriativo', [
        { urlClickTag: '', status: '', usuario: '', ...detalheCriativo },
      ]),
    )
    this.validstatus(
      status,
      idCriativo,
      detalheCriativo.subTipo,
      this.typeMsg,
      this.messageError,
      detalheCriativo,
    )
  }

  getCreativeDetailsStandart(creativeId) {
    this.digitalService
      .getCreativeDetails(creativeId)
      .pipe(first())
      .subscribe(
        (res: CreativeDetails) => {
          const status =
            res.criativoStatusHistoricoDTO[
              res.criativoStatusHistoricoDTO.length - 1
            ].status
          if (status !== TechnicalValidationCreativeStatus.PENDING) {
            this.setStandartDataAndValidate(res, status, creativeId)
          } else {
            this.loopObject[creativeId] = true
            this.creativeService.startCreativeLoop(
              (res, status, creativeId) => {
                this.setChangeCreativeDataAndValidate(res, status, creativeId)
              },
            )
          }
        },
        () => {
          this.openDialogErrorGeneric()
        },
      )
  }
  async validstatus(
    status: string,
    id,
    tipo,
    typeMsg,
    message,
    detalhe: CreativeDetails,
  ) {
    if (status === TechnicalValidationCreativeStatus.REPROVED) {
      this.typeMsg = 1
      this.messageError = detalhe.observacoes

      this.statusCriativo = false
    }
    if (status === TechnicalValidationCreativeStatus.APPROVED) {
      const format = this.checkFormat(tipo)

      if (!format) {
        this.typeMsg = 2
        this.messageError = message
        this.statusCriativo = false
        message = 'Não foi possível obter o formato do criativo enviado'
      }

      // exibir o modal para o upload dos criativos com origem do gerador
      if (this.originAdsStudio) {
        this.originAdsStudio = false
        this.notificationService
          .creativeAddSucesso(
            'Criativo adicionado com sucesso!',
            `Seu criativo foi adicionado e está pronto para ser associado <br> ao seu anúncio. Para exibi-lo na <strong>Galeria de Criativos</strong> e <br>torná-lo disponível para associação, atualize seus criativos.`,
            'ATUALIZAR CRIATIVOs',
          )
          .then(response => {
            if (response.isConfirmed) {
              this.updatedAprrovedList()
            }
          })
      }
    }
  }
  getDetailImage(image) {
    const URL = window.URL || window.webkitURL
    const Img = new Image()
    const filesToUpload = image
    const blob = new Blob([filesToUpload], { type: 'image' })
    Img.src = URL.createObjectURL(blob)
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    Img.onload = (e: any) => {
      const imgElement = e.path[0] as HTMLImageElement
      this.imageHeigth = imgElement.naturalHeight
      this.imageWidth = imgElement.naturalWidth
    }
  }

  handleError() {
    this.dialogHelper.closeDialog()
    // this.openDialogErroTimeOutFacil(idCreative)
  }
  openDialogErroTimeOutFacil(idCreative: any) {
    console.error('Method not implemented.', idCreative)
  }

  setStandartDataAndValidate(
    detalheCriativo: CreativeDetails,
    status: string,
    idCriativo,
  ) {
    this.typeMsg = 2
    this.messageError =
      detalheCriativo.erros && detalheCriativo.erros.length > 0
        ? detalheCriativo.erros[0]
        : null
    this.validstatus(
      status,
      idCriativo,
      detalheCriativo.subTipo,
      this.typeMsg,
      this.messageError,
      detalheCriativo,
    )
  }
  openDialogErrorGeneric() {
    {
      this.notificationService
        .globoSimCommon(
          'Ops, parece que tivemos um imprevisto.',
          ERROR_DIALOG_HTML,
          'ENTENDI',
        )
        .then(() => {
          this.router.navigate(['/meus-pedidos'], {
            queryParams: { tab: 'digital' },
          })
        })
    }
  }
  checkMessageInfooCretiveReproved(observation: string) {
    console.log(observation)
    if (observation.includes('peso')) {
      return 'Identificamos que o criativo selecionado excede o peso máximo suportado para este formato. Por favor, certifique-se de que o seu criativo siga todas as especificações técnicas.'
    } else if (observation.includes('Dimensão')) {
      return 'Identificamos que o criativo selecionado não possui a dimensão/proporção correta para este formato. Por favor, certifique-se de que o seu criativo siga todas as especificações técnicas.'
    } else if (observation.includes('extensão')) {
      return 'Identificamos que o arquivo selecionado possui uma extensão que é incompatível para este formato. Por favor, certifique-se de que o seu criativo siga todas as especificações técnicas.'
    } else {
      return `Identificamos que o criativo selecionado não seguiu as especificações técnicas de <strong>peso</strong>, <strong>dimensão</strong> ou  <strong>extensão</strong> definidas para este formato. Por favor, certifique-se de que o seu criativo siga todas as especificações técnicas.`
    }
  }
  checkFormat(type) {
    this.format = type
    switch (type) {
      case PreviewType.BILLBOARD:
        return true
      case PreviewType.RETANG:
        return true
      case PreviewType.MEGABANNER:
        return true
      case PreviewType.HALFPAGE:
        return true
      case PreviewType.MAXIBOARD:
        return true
      case PreviewType.CARROSSEL:
        return true
      default:
        return false
    }
  }

  uploadIaCreative(image) {
    this.sendFileS3(image)
    this.originAdsStudio = true
  }
  deleteOneAsset(asset: AssetsItem) {
    this.assetsItemService.deleteAsset([asset.id])
  }
  openGeradorIA() {
    this.geradorCriativoService.handlePanelState(true)
  }

  updatedAprrovedList() {
    this.addedComponente.getCreatives(false, false)
  }
}
