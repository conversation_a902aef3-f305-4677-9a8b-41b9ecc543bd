import { GeradorFormModel } from '../../models/gerador-form.model'
import { Component, OnInit } from '@angular/core'
import { Observable } from 'rxjs'
import { GeradorCriativosService } from '../../services/gerador-criativos.service'
import { MatDialog, MatDialogConfig } from '@angular/material/dialog'
import { DialogConfirmComponent } from '../../dialogs/dialog-confirm/dialog-confirm.component'
import { StepsDataService } from '../../services/steps-data.service'

@Component({
  selector: 'app-gerador-header',
  templateUrl: './gerador-header.component.html',
  styleUrls: ['./gerador-header.component.scss'],
})
export class HeaderComponent implements OnInit {
  currentStep$: Observable<GeradorFormModel>
  dialogConfig = new MatDialogConfig()

  constructor(
    private readonly service: GeradorCriativosService,
    private dialog: MatDialog,
    private stepsDataService: StepsDataService,
  ) {}

  ngOnInit(): void {
    this.currentStep$ = this.service.getCurrentStep()
  }

  openModalClose(
    onConfirmCallback: () => void,
    onCancelCallback?: () => void,
  ): void {
    this.dialogConfig.data = {
      message:
        'Se você fechar o Gerador de Criativos agora, as configurações atuais serão perdidas. Tem certeza de que deseja continuar?',
      buttonConfirmed: 'fechar gerador',
      headerTitle: 'Fechar gerador de criativos',
      title: 'Deseja fechar o Gerador de Criativos?',
      onConfirm: onConfirmCallback,
      onCancel: onCancelCallback,
      stepData$: this.currentStep$,
    }
    this.dialogConfig.width = '540px'
    this.dialogConfig.panelClass = ['delete-dialog', 'gtm-element-visibility']
    this.dialog.open(DialogConfirmComponent, this.dialogConfig)
  }

  callCloseModal() {
    this.openModalClose(this.cleanAndClose.bind(this), null)
  }

  handleClose(): void {
    this.service.setStepsInvalidFromSecond()
    this.service.sendClearRequest()
    this.stepsDataService.clearStepsData()
    this.service.handlePanelState(false)
  }

  cleanAndClose(): void {
    this.handleClose()
  }
}
