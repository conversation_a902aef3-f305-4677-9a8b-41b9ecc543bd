import { Component, Inject, OnInit, ViewChild } from '@angular/core'
import { FormGroup, FormBuilder, Validators } from '@angular/forms'
import { DigitalService } from '@app/core/services/no-auth/digital/digital.service'
import { NotificationService } from '@app/core/services/notification/notification.service'
import { UploadDigitalWithGloboAdsService } from '@app/core/services/upload-criativo-digital-with-globoads/upload-criativo-digital-with-globoads.service'
import {
  CreativeDetails,
  DigitalCreativeDetailsResponse,
} from '@app/shared/models/creative-details'
import { TechnicalValidationCreativeStatus } from '@app/shared/models/status-criativo-digital'
import {
  DigitalValidation,
  GenerateZipFileCreativeNative,
  PreSignedUrlDigital,
} from '@app/shared/models/upload-digital-video'
import { DialogHelper } from '@app/shared/utils/dialog-helper'
import { DigitalSenderHelper } from '@app/shared/utils/digital-sender-helper'
import {
  ERROR_DIALOG_HTML,
  TECHNICAL_VALIDATION_MESSAGE,
  TECHNICAL_VALIDATION_TITLE,
} from '@app/shared/utils/swal-templates'
import {
  MatLegacyDialogRef as MatDialogRef,
  MAT_LEGACY_DIALOG_DATA as MAT_DIALOG_DATA,
} from '@angular/material/legacy-dialog'
import { AppState } from '@app/store/app.model'
import { DigitalActions } from '@app/store/digital/digital.actions'
import { State, Store } from '@ngrx/store'
import { EMPTY } from 'rxjs'
import { first } from 'rxjs/operators'
import { PreviewType } from '../../preview-creative/data-model/data-model-consts'
import { AssociarCriativo } from '@app/shared/models/digital-sender'
import { ActivatedRoute, Router } from '@angular/router'
import { DigitalHelper } from '@app/shared/utils/digital-helper'
import { MatDialog } from '@angular/material/dialog'
import { CreativeService } from '@app/core/services/creative-service/creative-service.service'
import { ImageService } from '@app/features/digital/creative-manager/services/image.service'
import { v4 as uuidv4 } from 'uuid'
import { LocalStorageService } from '@app/core/services/local-storage/local-storage.service'
import { StorageKeysEnum } from '@app/shared/models/enums/storage-keys.enum'

@Component({
  selector: 'app-dialog-upload-especial-publicitario',
  templateUrl: './dialog-upload-especial-publicitario.component.html',
  styleUrls: ['./dialog-upload-especial-publicitario.component.scss'],
})
export class DialogUploadEspecialPublicitarioComponent implements OnInit {
  @ViewChild('closeButton') closeButton

  especialPublicitarioForm: FormGroup = new FormGroup({})
  requestIdsImages: string[] = []
  selectedFiles: File[] = []
  loopObject = {}
  changeCreative: boolean = false
  idDigitalCampaign: string = ''
  isPreview: boolean = false
  messageError: string
  statusCriativo: boolean
  format: string
  firstImageEspecialPubli: string
  secondImageEspecialPubli: string
  firstImageEspecialPubliRequestId: string
  secondImageEspecialPubliRequestId: string
  killProcess = false
  isLoading: boolean = false
  // Pega a URL atual da janela
  currentURL = ''

  // Verifica se a URL contém a palavra 'galeria'
  isFromGallery = false

  constructor(
    public dialogRef: MatDialogRef<DialogUploadEspecialPublicitarioComponent>,
    @Inject(MAT_DIALOG_DATA) public data,
    private formBuilder: FormBuilder,
    private uploadMaterialService: UploadDigitalWithGloboAdsService,
    private notificationService: NotificationService,
    private digitalSenderHelper: DigitalSenderHelper,
    private digitalService: DigitalService,
    protected state: State<AppState>,
    protected store: Store<AppState>,
    private dialogHelper: DialogHelper,
    private route: ActivatedRoute,
    private router: Router,
    private digitalHelper: DigitalHelper,
    private dialog: MatDialog,
    private creativeService: CreativeService,
    private imageService: ImageService,
    private uploadService: UploadDigitalWithGloboAdsService,
    private localStorageService: LocalStorageService,
  ) {}

  ngOnInit() {
    this.currentURL = window.location.href

    // checking if the URL contains the word 'gallery'
    this.isFromGallery = this.currentURL.includes('galeria')

    this.isPreview = this.data.isPreview
    this.initForm()
    this.checkChangeCreative()
    this.getIdCampaign()
    this.checkIsPreview()
    this.creativeService.killInterval$.subscribe(() => {
      this.killProcess = true
    })
  }

  get titleControl() {
    return this.especialPublicitarioForm.get('title')
  }

  get subTitleControl() {
    return this.especialPublicitarioForm.get('subTitle')
  }

  private initForm() {
    this.especialPublicitarioForm = this.formBuilder.group({
      title: [null, [Validators.required, Validators.maxLength(80)]],
      subTitle: [null, [Validators.required, Validators.maxLength(180)]],
    })
  }

  reciveImage(requestIds: string) {
    this.requestIdsImages.push(requestIds)
    this.updateRequestIds()
  }

  closeDialog() {
    this.closeButton.nativeElement.click() // Fechar o modal programaticamente
  }

  getIdCampaign() {
    if (!this.changeCreative) {
      this.idDigitalCampaign = this.digitalHelper
        .getIdCamapnhaDigital(this.route.snapshot.paramMap.get('id'))
        .toString()
    } else {
      const campaignData = JSON.parse(
        atob(this.route.snapshot.paramMap.get('campaignObject')),
      )
      this.idDigitalCampaign = campaignData['campanhaId']
    }

    if (this.state.getValue()?.digital?.id === undefined) {
      this.store.dispatch(DigitalActions.setInitialState())
    }
  }

  async sendCreativeZip(
    url: string,
    creativeName: string,
    creativeExternalId: string,
  ) {
    await this.digitalSenderHelper
      .sendCreative(url, null, creativeName, creativeExternalId)
      .then(response => {
        if (response.id) {
          this.creativeService.setIdsPending(response.id)
          this.getCreativeZip(response.id)
        }
      })
  }

  getCreativeZip(creativeId) {
    this.digitalService
      .getCreativeDetails(creativeId)
      .pipe(first())
      .subscribe(
        (response: CreativeDetails) => {
          const status =
            response.criativoStatusHistoricoDTO[
              response.criativoStatusHistoricoDTO.length - 1
            ].status
          if (status !== TechnicalValidationCreativeStatus.PENDING) {
            this.setChangeCreativeDataAndValidate(response, status, creativeId)
          } else {
            this.loopObject[creativeId] = true
            this.creativeService.startCreativeLoop(
              (res, status, creativeId) => {
                this.setChangeCreativeDataAndValidate(res, status, creativeId)
              },
            )
          }
        },
        () => {
          this.openDialogErrorGeneric()
        },
      )
  }

  openDialogErrorGeneric() {
    {
      this.notificationService
        .globoSimCommon(
          'Ops, parece que tivemos um imprevisto.',
          ERROR_DIALOG_HTML,
          'ENTENDI',
        )
        .then(() => {
          this.router.navigate(['/meus-pedidos'], {
            queryParams: { tab: 'digital' },
          })
        })
    }
  }

  handleTimeOut(status: string, counter: number, id) {
    if (status === TechnicalValidationCreativeStatus.PENDING && counter === 4) {
      this.loopObject[id] = false
      this.handleError()
      return EMPTY
    }
  }

  onImageSelected(file: File) {
    this.selectedFiles.push(file)
  }

  async sendCreative() {
    if (this.selectedFiles.length < 2) {
      this.notificationService.showToastWarn('Selecione 2 imagens.')
      return
    }

    this.isLoading = true
    try {
      const preSignedUrls = await this.getPreSignedUrls(this.selectedFiles)
      this.updateRequestIdsFromPreSignedUrls(preSignedUrls)

      // Adicionar os uploads para o S3
      await this.uploadFilesToS3(preSignedUrls, this.selectedFiles)
      this.notificationService.showToastSuccess('Imagens enviadas com sucesso!')

      const itemsToZip: GenerateZipFileCreativeNative = {
        requestId: this.requestIdsImages,
        title: this.especialPublicitarioForm.get('title').value,
        subtitle: this.especialPublicitarioForm.get('subTitle').value,
        createCreative: false,
      }

      this.uploadService.generateZipFileCreativeNative(itemsToZip).subscribe(
        response => {
          this.isLoading = false // Reabilitar os botões antes de fechar
          this.closeButton.nativeElement.click() // Fechar o modal após o envio
          this.creativeService.addOrUpdateCreativeDetails([
            new DigitalCreativeDetailsResponse({
              id: Math.random(),
              nome: response.zipName,
              subTipo: 'Chamada Especial Publicitário',
              observacoes: 'loading',
            }),
          ])
          this.sendCreativeZip(
            response.url,
            response.zipName,
            response.external_id,
          )
            .then(() => {
              this.notificationService.showToastSuccess(
                'Criativo enviado com sucesso! Em breve você poderá acompanhar o status do seu criativo.',
              )
            })
            .catch(error => {
              console.error('Erro ao enviar o criativo:', error)
              this.notificationService.showToastError(
                'Ocorreu um erro ao enviar o criativo',
              )
              this.isLoading = false // Reabilitar os botões em caso de erro
            })
        },
        error => {
          console.error('Erro ao gerar o arquivo ZIP:', error)
          this.notificationService.showToastError(
            'Ocorreu um erro ao gerar o arquivo ZIP',
          )
          this.isLoading = false // Reabilitar os botões em caso de erro
        },
      )
    } catch (error) {
      console.error('Erro durante o upload das imagens:', error)
      this.notificationService.showToastError(
        'Ocorreu um erro durante o upload das imagens',
      )
      this.isLoading = false
    }
  }

  async getPreSignedUrls(files: File[]): Promise<PreSignedUrlDigital[]> {
    const userCnpj = this.localStorageService.get<string>(
      StorageKeysEnum.userCNPJ,
    )

    const payload: DigitalValidation = {
      agencyDocument: '',
      origin: 'globosim',
      customerDocument: userCnpj,
      items: files.map(file => ({
        contentType: file.type,
        fileName: file.name,
        requestId: uuidv4(),
        fileSize: file.size,
      })),
    }
    // Adicionando o return para garantir que a promessa seja retornada corretamente
    return this.uploadService.digitalValidation(payload).toPromise()
  }

  updateRequestIdsFromPreSignedUrls(preSignedUrls: PreSignedUrlDigital[]) {
    this.requestIdsImages = preSignedUrls.map(urlInfo => urlInfo.requestId)
    this.updateRequestIds()
  }

  async uploadFilesToS3(preSignedUrls: PreSignedUrlDigital[], files: File[]) {
    const uploadPromises = preSignedUrls.map((urlInfo, index) => {
      return this.uploadService
        .uploadToS3(urlInfo.signUrl, files[index])
        .toPromise()
        .then(() => {})
        .catch(error => {
          this.notificationService.showToastWarn(
            'Ocorreu um erro durante o upload da imagem',
          )
          throw error
        })
    })

    return Promise.all(uploadPromises)
  }

  setChangeCreativeDataAndValidate(
    detalheCriativo: CreativeDetails,
    status: string,
    idCriativo,
  ) {
    this.messageError =
      detalheCriativo.erros && detalheCriativo.erros.length > 0
        ? detalheCriativo.erros[0]
        : null
    this.store.dispatch(
      DigitalActions.changeValue('listCriativo', [
        { urlClickTag: '', status: '', usuario: '', ...detalheCriativo },
      ]),
    )
    this.validstatus(
      status,
      idCriativo,
      detalheCriativo.subTipo,
      this.messageError,
    )
  }

  async validstatus(status: string, id, tipo, message) {
    if (status === TechnicalValidationCreativeStatus.REPROVED) {
      this.messageError = message
      if (this.changeCreative) {
        this.store.dispatch(DigitalActions.changeValue('listCriativo', []))
      }
      this.statusCriativo = false
      this.openDialogErrorSendFile()
    }
    if (status === TechnicalValidationCreativeStatus.APPROVED) {
      const format = this.checkFormat(tipo)
      if (!format) {
        this.messageError = message
        this.statusCriativo = false
        this.openDialogErrorSendFile()

        if (this.changeCreative) {
          this.store.dispatch(DigitalActions.changeValue('listCriativo', []))
        }
      } else {
        if (!this.changeCreative && !this.isFromGallery) {
          this.connectCreativeCampaign(this.idDigitalCampaign, parseInt(id))
            .pipe(first())
            .subscribe(
              () => {
                this.digitalHelper
                  .getDataCampaign(this.idDigitalCampaign)
                  .then(() => {
                    this.dialog.closeAll()
                  })
              },
              () => {
                this.openDialogErrorGeneric()
              },
            )
        }
      }
    }
  }

  handleError() {
    this.dialogHelper.closeDialog()
    this.openDialogErroTimeOutFacil()
  }

  openDialogErrorSendFile() {
    this.notificationService
      .globoSimCommon(
        TECHNICAL_VALIDATION_TITLE,
        TECHNICAL_VALIDATION_MESSAGE,
        'ENTENDI',
      )
      .then(() => {
        this.getDataCampaign()
        this.dialog.closeAll()
      })
  }

  openDialogErroTimeOutFacil() {
    this.notificationService
      .globoSimCommon(
        'Ops, tivemos um imprevisto',
        'O tempo de integração com o serviço de validação de criativos foi excedido.Tente novamente em alguns segundos.',
        'TENTAR NOVAMENTE',
      )
      .then()
  }

  checkFormat(type) {
    this.format = type
    switch (type) {
      case PreviewType.ESPECIAL_PUBLIC:
        return true
      default:
        return false
    }
  }

  checkChangeCreative() {
    this.changeCreative = this.router.url.includes('troca')
  }

  connectCreativeCampaign(campanhaId: string, criativoId: number) {
    const prospAssociate: AssociarCriativo = { campanhaId, criativoId }
    return this.digitalService.associarCriativoCampanha(prospAssociate)
  }

  async getDataCampaign() {
    const campaignData = await this.digitalService
      .recuperarCampanhaDigital(this.idDigitalCampaign)
      .toPromise()
    this.digitalHelper.setDigitalCapaignAllData(campaignData)
  }

  checkIsPreview() {
    if (this.isPreview) {
      this.firstImageEspecialPubli = this.data.image1
      this.secondImageEspecialPubli = this.data.image2
      this.especialPublicitarioForm.setValue({
        title: this.data.title,
        subTitle: this.data.subtitle,
      })
    }
  }

  onDeleteImage(requestId: string) {
    const index = this.requestIdsImages.indexOf(requestId)
    if (index > -1) {
      this.requestIdsImages.splice(index, 1)
    }
    this.updateRequestIds()
  }

  updateRequestIds() {
    this.firstImageEspecialPubliRequestId = this.requestIdsImages[0] || null
    this.secondImageEspecialPubliRequestId = this.requestIdsImages[1] || null
  }
}
