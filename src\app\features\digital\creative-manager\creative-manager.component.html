<div class="flex flex-col gap-4 max-w-full">
  <section
    class="py-4 lg:pt-2 lg:pb-4 text-content-primary-light mx-6 lg:mx-8 border-b border-border-neutral-light"
  >
    <adtech-typography
      variant="title"
      size="large"
      styles="text-content-secondary-light"
      >Galeria de criativos</adtech-typography
    >
  </section>
  <section class="flex flex-col gap-4 max-w-full">
    <div class="sm:flex sm:justify-between mx-6 lg:mx-8">
      <div class="flex gap-3 items-center">
        <mat-icon class="!w-auto !h-auto text-[64px] material-symbols-outlined"
          >laptop_mac</mat-icon
        >
        <div class="flex flex-col">
          <adtech-typography
            variant="body"
            size="small"
            styles="text-content-secondary-light"
            >plataforma</adtech-typography
          >
          <adtech-typography
            variant="title"
            size="large"
            styles="text-content-secondary-light"
            >digital</adtech-typography
          >
        </div>
      </div>

      <div class="flex items-center">
        <button
          glbButton
          theme="purple-gradient"
          (click)="redirectAddCriativo()"
        >
          adicionar criativo
        </button>
      </div>
    </div>

    <app-custom-tabs
      class="ml-6 sm:mx-6 lg:mx-8 max-w-full"
      [showSeparatorAfter]="[3]"
      [tabTitlesInput]="[
        'adicionados',
        'em análise',
        'aprovados',
        'reprovados',
        'Minhas imagens',

      ]"
    >
      <ng-template #tab1>
        <div class="mr-6 md:mr-0 max-w-full">
          <app-added></app-added>
        </div>
      </ng-template>

      <ng-template #tab2>
        <div class="mr-6 md:mr-0 max-w-full">
          <app-validation></app-validation>
        </div>
      </ng-template>

      <ng-template #tab3>
        <div class="mr-6 md:mr-0 max-w-full">
          <app-approved></app-approved>
        </div>
      </ng-template>
      <ng-template #tab4>
        <div class="mr-6 md:mr-0 max-w-full">
          <app-failed></app-failed>
        </div>
      </ng-template>
      <ng-template #tab5>
        <div class="mr-6 md:mr-0 max-w-full">
          <div><app-assets-images-list></app-assets-images-list></div>
        </div>
      </ng-template>
    </app-custom-tabs>
  </section>
</div>

<app-creative-info [drawerOpen]="drawerOpen"></app-creative-info>
<app-asstes-info [drawerOpen]="drawerAssetsOpen" (deleteAsset)="deleteOneAsset($event)"></app-asstes-info>
<app-gerador-criativos></app-gerador-criativos>
