<div class="flex flex-col rounded-md lg:overflow-hidden">
  <div class="w-full overflow-x-auto lg:overflow-hidden scrollbar-hide max-w-full">
    <div [attr.class]="'flex relative border-b border-border-neutral-light gap-6 min-w-[450px] ' + styles">
      <ng-container *ngFor="let tab of tabs; let i = index">
        <div class="flex items-center">
          <div
            class="cursor-pointer py-5 text-center relative transition-colors duration-300 ease-in-out leading-20 tracking-20 text-gray-600 font-book text-30"
            #tabTitle
            [ngClass]="{
              'text-gray-900 font-medium': i === selectedIndex,
              'hover:bg-gray-100': i !== selectedIndex
            }"
            (click)="selectTab(i)"
          >
            {{ tab.title }}
          </div>
          <!-- Adicione a barra vertical aqui -->
          <div *ngIf="showSeparatorAfter.includes(i)" class="w-px h-5 bg-gray-300 ml-3"></div>
        </div>
      </ng-container>
      <div class="absolute bottom-0 left-0 h-1 bg-[#5C43FA] transition-transform duration-300 ease-in-out"
           [ngStyle]="{ 'width': indicatorWidth + 'px', 'transform': 'translateX(' + indicatorPosition + 'px)' }">
      </div>
    </div>
  </div>
  <ng-container *ngIf="tabs[selectedIndex]" [@tabAnimation]>
    <ng-container *ngTemplateOutlet="tabs[selectedIndex].content"></ng-container>
  </ng-container>
</div>