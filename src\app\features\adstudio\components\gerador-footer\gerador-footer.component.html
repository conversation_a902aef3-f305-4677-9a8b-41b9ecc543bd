<div
  class="footer-container w-100 d-flex justify-content-between"
  *ngIf="currentStep && isVisible"
>
  <div class="footer-step d-flex align-items-center">
    <p>{{ currentStep.step === 6 ? 5 : currentStep.step }} / 5</p>
    <span class="check" [ngClass]="{ checked: currentStep.valid }"></span>
    <p class="title footer-description">{{ currentStep.component.title }}</p>
  </div>
  <div class="footer-controls d-flex gap-4">
    <app-gerador-buttons
      *ngIf="currentStep.component.can_back"
      theme="secondary"
      label="Voltar"
      iconSrc="next-arrow-white.svg"
      iconPosition="right"
      (buttonClick)="handleBackStep(currentStep.step)"
      [step]="currentStep"
    >
    </app-gerador-buttons>
    <app-gerador-buttons
      theme="primary"
      [label]="currentStep.component.step_title"
      [iconSrc]="currentStep.component.step_icon ?? null"
      [short_label]="currentStep.component.step_title_short"
      iconPosition="right"
      [disabled]="!currentStep.valid"
      (buttonClick)="handleNextStep(currentStep.step)"
      [step]="currentStep"
    >
    </app-gerador-buttons>
  </div>
</div>