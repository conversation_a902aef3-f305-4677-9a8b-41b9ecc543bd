import { Component, Inject } from '@angular/core'
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog'
import { DialogGenericData } from '../models/model'

@Component({
  selector: 'app-dialog-generic-assets',
  templateUrl: './dialog-generic-assets.component.html',
  styleUrls: ['./dialog-generic-assets.component.scss'],
})
export class DialogGenericAssetsComponent {
  constructor(
    public dialogRef: MatDialogRef<DialogGenericAssetsComponent>,
    @Inject(MAT_DIALOG_DATA) public data: DialogGenericData,
  ) {}

  onPrimaryAction(): void {
    if (this.data.primaryButton?.returnValue !== undefined) {
      this.dialogRef.close(this.data.primaryButton.returnValue)
    } else {
      this.dialogRef.close(true)
    }
  }

  onSecondaryAction(): void {
    if (this.data.secondaryButton?.returnValue !== undefined) {
      this.dialogRef.close(this.data.secondaryButton.returnValue)
    } else {
      this.dialogRef.close(false)
    }
  }

  closeDialog(): void {
    this.dialogRef.close()
  }

  getDataAttributes(
    buttonType: 'primary' | 'secondary',
  ): Record<string, string> {
    const button =
      buttonType === 'primary'
        ? this.data.primaryButton
        : this.data.secondaryButton

    if (!button?.dataAttrs) {
      return {}
    }

    return {
      'data-element': button.dataAttrs.element || '',
      'data-state': button.dataAttrs.state || '',
      'data-area': button.dataAttrs.area || '',
      'data-section': button.dataAttrs.section || '',
      'data-label': button.dataAttrs.label || '',
    }
  }
}
