import { Injectable } from '@angular/core'
import { Subject, BehaviorSubject } from 'rxjs'

@Injectable({
  providedIn: 'root',
})
export class FooterActionService {
  private eventSource = new Subject<{
    action: 'confirmar' | 'voltar'
    step: number
  }>()

  event$ = this.eventSource.asObservable()

  private footerVisibilitySource = new BehaviorSubject<boolean>(true)
  footerVisibility$ = this.footerVisibilitySource.asObservable()

  emitEvent(action: 'confirmar' | 'voltar', step: number): void {
    this.eventSource.next({ action, step })
  }

  hideFooter(): void {
    this.footerVisibilitySource.next(false)
  }

  showFooter(): void {
    this.footerVisibilitySource.next(true)
  }

  toggleFooterVisibility(): void {
    const currentValue = this.footerVisibilitySource.getValue()
    this.footerVisibilitySource.next(!currentValue)
  }
}
