<div
  class="c-dialog-display-format gtm-element-visibility"
  id="c-dialog-display-format"
  [class.open]="isOpen"
  data-element="painel_lateral"
  data-state="activated"
  data-section="adicionar_criativo"
  [attr.data-label]=""
  data-area="envio_criativo"
>
  <ng-container *ngIf="termosAceito; else telaTermosDeUso">
    <div class="c-dialog-display-format__header">
      <app-gerador-header />
    </div>
    <div
      class="c-dialog-display-format__content"
      (imageGenerated)="onImageGenerated($event)"
    >
      <app-gerador-form />
    </div>
    <div  *ngIf="isFooterVisible" class="c-dialog-display-format__actions">
      <app-gerador-footer />
    </div>
  </ng-container>
</div>

<ng-template #telaTermosDeUso>
  <app-gerador-termos-uso
    (resposta)="handleAceite($event)"
  ></app-gerador-termos-uso>
</ng-template>

<app-blackout-screen
  *ngIf="isOpen"
  (onClickInside)="closeSidebar()"
></app-blackout-screen>
