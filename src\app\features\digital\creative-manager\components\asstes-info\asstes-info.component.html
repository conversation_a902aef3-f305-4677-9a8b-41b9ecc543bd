<app-slide-card
  class="rounded-t-2xl rounded-bl-2xl"
  [manualToggle]="drawerOpen"
  [nonSingleton]="true"
>
  <div class="flex w-full h-screen">
    <div class="min-h-screen min-w-5 lg:min-w-0 lg:h-0 bg-black/60"></div>
    <div
      class="flex flex-col relative w-full h-full max-w-[calc(100%-21px)] lg:max-w-full min-h-0"
    >
      <app-custom-icon
        (click)="close()"
        class="h-fit absolute -left-5"
        size="40"
        icon="new-close"
      ></app-custom-icon>
      <div class="flex flex-col gap-1 pt-8 px-4 lg:px-6 bg-white">
        <adtech-typography
          variant="label"
          size="small"
          styles="text-content-secondary-light"
          >Minhas imagens
        </adtech-typography>
        <adtech-typography
          variant="title"
          size="medium"
          as="div"
          styles="text-content-primary-light"
          >Informações da imagem</adtech-typography
        >
      </div>

      <div
        class="flex-grow overflow-y-auto max-h-[calc(115vh-271px)] pb-8 pt-4 lg:pt-6 min-h-0 bg-background-secondary-light mt-3"
      >
        <div class="px-4 lg:px-6">
          <div
            class="overflow-hidden flex items-center justify-center w-full rounded-2xl bg-overlay-luminosity-negative_1-light"
          >
            <div class="h-full w-full">
              <div
                class="flex items-center justify-center w-full h-full"
                *ngIf="isLoading"
              >
                <app-spinner></app-spinner>
              </div>
              <div
                *ngIf="!isLoading"
                class="flex items-center justify-center w-full h-full"
              >
                <img [src]="assetInfo?.src" />
              </div>
            </div>
          </div>

          <img
            *ngIf="assetInfo?.favorite"
            vaultSrc="icons/favorite_full.svg"
            class="left-3 relative w-8 h-8 top-[-50px] scale-130 bg-white rounded-full ng-star-inserted p-2"
          />
        </div>

        <div class="flex flex-col px-4 lg:px-6">
          <div
            class="flex justify-between items-center py-4 border-b border-neutral-200"
          >
            <adtech-typography
              variant="body"
              size="small"
              class="adtech-dots"
              styles="!text-content-secondary-light"
            >
              título</adtech-typography
            >
            <adtech-typography
              variant="body"
              size="small"
              class="adtech-dots"
              as="div"
              >{{ assetInfo?.name }}</adtech-typography
            >
          </div>

          <div
            class="flex justify-between items-center py-4 border-b border-neutral-200"
          >
            <adtech-typography
              variant="body"
              size="small"
              styles="!text-content-secondary-light"
              >dimensao</adtech-typography
            >
            <adtech-typography variant="body" size="small" as="div">{{
              assetInfo?.dimension
            }}</adtech-typography>
          </div>

          <div
            class="flex justify-between items-center py-4 border-b border-neutral-200"
          >
            <adtech-typography
              variant="body"
              size="small"
              styles="!text-content-secondary-light"
              >extensão</adtech-typography
            >
            <adtech-typography variant="body" size="small" as="div">{{
              assetInfo?.extension
            }}</adtech-typography>
          </div>

          <div
            class="flex justify-between items-center py-4 border-b border-neutral-200"
          >
            <adtech-typography
              variant="body"
              size="small"
              styles="!text-content-secondary-light"
              >tamanho</adtech-typography
            >
            <adtech-typography variant="body" size="small" as="div">{{
              transformSize(assetInfo?.size)
            }}</adtech-typography>
          </div>

          <div
            class="flex justify-between items-center py-4 border-b border-neutral-200"
          >
            <adtech-typography
              variant="body"
              size="small"
              styles="!text-content-secondary-light"
              >upload</adtech-typography
            >
            <adtech-typography variant="body" size="small" as="div">{{
              formattedDate(assetInfo?.date)
            }}</adtech-typography>
          </div>
          <div
            *ngIf="!assetInfo?.favorite"
            class="flex justify-between items-center py-4 border-b border-neutral-200"
          >
            <adtech-typography
              variant="body"
              size="small"
              styles="!text-content-secondary-light"
              >expiração</adtech-typography
            >
            <div class="flex items-center">
              <adtech-typography variant="body" size="small" as="div">{{
                formattedDate(assetInfo?.date)
              }}</adtech-typography>
              <adtech-typography
                variant="body"
                size="small"
                [ngClass]="getExpirationColor()"
                class="ml-1"
                as="div"
              >
                {{ getExpirationMessage() }}
              </adtech-typography>
            </div>
          </div>

          <div
            *ngIf="assetInfo?.favorite"
            class="flex items-center text-gray-700 text-sm bg-[#ffffff] rounded-md p-2 !w-full md:w-auto md:ml-auto md:self-auto justify-center mt-4"
          >
            <div
              class="w-6 h-6 rounded-full bg-white border border-gray-200 flex items-center justify-center mr-2"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-4 w-4 text-indigo-600"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fill-rule="evenodd"
                  d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z"
                  clip-rule="evenodd"
                />
              </svg>
            </div>
            <span
              >Imagens marcadas como favoritas não têm prazo de validade.</span
            >
          </div>
        </div>
      </div>

      <div class="flex items-center justify-center py-6 bg-white mt-6">
        <button
          *ngIf="canDelete"
          class="mx-auto !w-fit"
          glbButton
          theme="warning"
          (click)="handleDelete()"
          data-element="button"
          data-state="activated"
          data-area="biblioteca_midia"
          data-section="assets.informacoes_do_asset"
          data-label="excluir_asset"
        >
          Excluir Imagem
        </button>
      </div>
    </div>
  </div>
</app-slide-card>
