/* eslint-disable @typescript-eslint/no-explicit-any */
import { SelectOptionReduxHelper } from './shared/components/select-options/helper/select-option-redux-helper'
import {
  NgModule,
  LOCALE_ID,
  CUSTOM_ELEMENTS_SCHEMA,
  APP_INITIALIZER,
  ErrorHandler,
} from '@angular/core'
import { Router } from '@angular/router'
import * as Sentry from '@sentry/angular-ivy'

import { AppComponent } from '@app/app.component'
import { BrowserAnimationsModule } from '@angular/platform-browser/animations'
import { AppRoutingModule } from '@app/app-routing.module'
import { HttpClientModule, HTTP_INTERCEPTORS } from '@angular/common/http'
import { CustomIconService } from '@app/core/services/custom-icon/custom-icon.service'
import {
  HttpRequestInterceptor,
  HttpCancelService,
} from '@app/core/interceptors/http-request-interceptor'
import { MatPaginatorIntlPtBr } from '@app/core/mat-paginator-intl-pt-br'
import { registerLocaleData } from '@angular/common'
import localePt from '@angular/common/locales/pt'
import { Globals } from './app.globals'
import { QRCodeModule } from 'angularx-qrcode'
import { DialogAvisoGenericaComponent } from '@app/shared/components/dialogs/dialog-aviso-generica/dialog-aviso-generica.component'
import { FormsModule } from '@angular/forms'
import { ActionReducer, MetaReducer, StoreModule } from '@ngrx/store'
import { ProfileRedirect } from '@app/shared/utils/profile-redirect'
import { rootReducer } from './store/root.reducer'
import { MatIconModule } from '@angular/material/icon'
import { MatToolbarModule } from '@angular/material/toolbar'
import { MatLegacyPaginatorIntl as MatPaginatorIntl } from '@angular/material/legacy-paginator'
import { localStorageSync } from 'ngrx-store-localstorage'

registerLocaleData(localePt)

export function localStorageSyncReducer(
  reducer: ActionReducer<any>,
): ActionReducer<any> {
  return localStorageSync({
    keys: ['agencyForm'],
    rehydrate: true,
  })(reducer)
}

const metaReducers: Array<MetaReducer<any, any>> = [localStorageSyncReducer]

import * as Hammer from 'hammerjs'
import {
  HammerGestureConfig,
  HammerModule,
  HAMMER_GESTURE_CONFIG,
} from '@angular/platform-browser'
import { UnsafePipe } from './shared/pipes/unsafe.pipe'
import { PaymentFeeStorageService } from './core/services/payment-fee-storage/payment-fee-storage.service'
import { ProducerStorageService } from './core/services/producer-storage-service/producer-storage.service'
import { AppInitService } from 'src/app-init-service'
import { StoreDevtoolsModule } from '@ngrx/store-devtools'
import { environment } from '../environments/environment'
import { LoadingPageComponent } from './pages/loading-page/loading-page.component'
import { LogoutComponent } from './pages/logout/logout.component'
import { ChatFormModule } from './shared/components/chat-form/chat-form.module'
import { AssetsGalleryService } from './features/adstudio/services/assets-gallery.service'

registerLocaleData(localePt)

export class MyHammerConfig extends HammerGestureConfig {
  overrides = <any>{
    swipe: { direction: Hammer.DIRECTION_ALL },
    pinch: { enable: false },
    rotate: { enable: false },
  }
}

export function initKeyCloakSession(appInitService: AppInitService) {
  return (): Promise<any> => {
    return appInitService.initKeyCloakSession()
  }
}

@NgModule({
  declarations: [
    AppComponent,
    DialogAvisoGenericaComponent,
    UnsafePipe,
    LoadingPageComponent,
    LogoutComponent,
  ],
  imports: [
    BrowserAnimationsModule,
    AppRoutingModule,
    HttpClientModule,
    MatIconModule,
    MatToolbarModule,
    FormsModule,
    StoreModule.forRoot(rootReducer, { metaReducers }),
    HammerModule,
    QRCodeModule,
    StoreDevtoolsModule.instrument({
      maxAge: 25,
      logOnly: environment.production,
    }),
    ChatFormModule,
  ],
  providers: [
    AssetsGalleryService,
    [PaymentFeeStorageService],
    [ProducerStorageService],
    CustomIconService,
    HttpCancelService,
    SelectOptionReduxHelper,
    {
      provide: HTTP_INTERCEPTORS,
      useClass: HttpRequestInterceptor,
      multi: true,
    },
    { provide: MatPaginatorIntl, useClass: MatPaginatorIntlPtBr },
    { provide: LOCALE_ID, useValue: 'pt' },
    Globals,
    ProfileRedirect,
    {
      provide: HAMMER_GESTURE_CONFIG,
      useClass: MyHammerConfig,
    },
    AppInitService,
    {
      provide: APP_INITIALIZER,
      useFactory: initKeyCloakSession,
      deps: [AppInitService],
      multi: true,
    },
    // Sentry configuration
    {
      provide: ErrorHandler,
      useValue: Sentry.createErrorHandler({
        showDialog: environment.production ? false : true, // Show error dialog only in non-production
      }),
    },
    {
      provide: Sentry.TraceService,
      deps: [Router],
    },
    {
      provide: APP_INITIALIZER,
      useFactory: () => () => {},
      deps: [Sentry.TraceService],
      multi: true,
    },
  ],
  bootstrap: [AppComponent],
  schemas: [
    CUSTOM_ELEMENTS_SCHEMA, // Tells Angular we will have custom tags in our templates
  ],
})
export class AppModule {}
