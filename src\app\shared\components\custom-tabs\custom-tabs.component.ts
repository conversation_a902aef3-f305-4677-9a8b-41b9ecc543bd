import {
  Component,
  ContentChildren,
  QueryList,
  AfterContentInit,
  TemplateRef,
  Input,
  ViewChildren,
  ElementRef,
  AfterViewChecked,
  Output,
  EventEmitter,
  ChangeDetectorRef,
} from '@angular/core'
import { trigger, state, style, animate, transition } from '@angular/animations'

@Component({
  selector: 'app-custom-tabs',
  templateUrl: './custom-tabs.component.html',
  animations: [
    trigger('tabAnimation', [
      state('void', style({ opacity: 0, transform: 'translateY(8px)' })),
      state('*', style({ opacity: 1, transform: 'translateY(0)' })),
      transition('void => *', [
        animate('300ms cubic-bezier(0.25, 0.46, 0.45, 0.94)'),
      ]),
      transition('* => void', [
        animate('300ms cubic-bezier(0.25, 0.46, 0.45, 0.94)'),
      ]),
    ]),
  ],
})
export class CustomTabsComponent implements AfterContentInit, AfterViewChecked {
  @ContentChildren(TemplateRef) contentTemplates!: QueryList<
    TemplateRef<HTMLElement>
  >
  @ViewChildren('tabTitle') tabTitles!: QueryList<ElementRef>

  @Input() tabTitlesInput: string[] = []
  @Input() styles: string
  @Output() onTabChange = new EventEmitter<number>()
  @Input() showSeparatorAfter: number[] = []

  tabs: { title: string; content: TemplateRef<HTMLElement> }[] = []
  selectedIndex = 0
  indicatorPosition = 0
  indicatorWidth = 0

  constructor(private cdr: ChangeDetectorRef) {}

  ngAfterContentInit() {
    this.tabs = this.contentTemplates.map((template, index) => ({
      title: this.tabTitlesInput[index] || `Tab ${index + 1}`,
      content: template,
    }))
    this.updateIndicator()
    this.cdr.detectChanges()
  }

  ngAfterViewChecked() {
    this.updateIndicator()
    this.cdr.detectChanges()
  }

  selectTab(index: number) {
    this.selectedIndex = index
    this.updateIndicator()
    this.onTabChange.emit(this.selectedIndex)
  }

  private updateIndicator() {
    if (this.tabTitles?.length > 0) {
      const activeTab =
        this.tabTitles.toArray()[this.selectedIndex].nativeElement
      this.indicatorPosition = activeTab.offsetLeft
      this.indicatorWidth = activeTab.offsetWidth
    }
  }
}
