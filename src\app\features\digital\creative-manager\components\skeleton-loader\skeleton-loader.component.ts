import { Component, Input } from '@angular/core'

@Component({
  selector: 'app-skeleton-loader',
  templateUrl: './skeleton-loader.component.html',
  styleUrls: ['./skeleton-loader.component.scss'],
})
export class SkeletonLoaderComponent {
  @Input() count: number = 1
  @Input() width: string = '100%'
  @Input() height: string = '20px'
  @Input() borderRadius: string = '4px'
  @Input() direction: 'row' | 'column' = 'column' // Direção do layout
}
