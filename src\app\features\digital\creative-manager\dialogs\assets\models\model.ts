// dialog-generic.interface.ts
export interface DialogGenericData {
  headerTitle?: string
  title: string
  message: string
  primaryButton?: {
    text: string
    theme: DialogButtonTheme
    returnValue?: any
    dataAttrs?: DialogDataAttributes
  }

  secondaryButton?: {
    text: string
    theme: DialogButtonTheme
    returnValue?: any
    dataAttrs?: DialogDataAttributes
  }

  // Contexto de uso da galeria
  context?: 'flow4' | 'default'
  isFlow4Selection?: boolean

  [key: string]: any
}

// Temas possíveis para os botões
export type DialogButtonTheme =
  | 'basic'
  | 'basic-purple-variation'
  | 'primary'
  | 'warning'
  | 'warning-fill'
  | 'success'

// Interface para atributos de dados para analytics
export interface DialogDataAttributes {
  element?: string
  state?: string
  area?: string
  section?: string
  label?: string
}
