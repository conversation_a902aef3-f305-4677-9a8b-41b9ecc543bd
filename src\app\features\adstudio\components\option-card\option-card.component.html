<div class="w-full max-w-full" (click)="onClick()">
  <div
    [ngClass]="[
      'bg-gray-50 rounded-xl p-6 flex flex-col items-center relative mb-3 w-full',
      isSelected ? 'selected-card' : ''
    ]"
  >
    <div class="mb-2">
      <div class="icon-gradient w-16 h-16 rounded-2xl flex items-center justify-center">
        <img [src]="'assets/imgs/icons/'+ iconSrc" alt="{{ title }}" class="w-7 h-7 object-contain">
      </div>
    </div>

    <h2 class="text-blue-600 font-medium text-center">{{ title }}</h2>
  </div>

  <p class="text-gray-700 text-sm text-center px-4 mb-6">{{ description }}</p>
</div>