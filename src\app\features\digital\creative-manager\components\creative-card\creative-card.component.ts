import {
  Component,
  EventEmitter,
  Input,
  On<PERSON><PERSON>roy,
  OnInit,
  Output,
} from '@angular/core'
import { CreativeItem } from '@app/core/services/no-auth/digital/model/digital-interface-utils'
import { Subscription } from 'rxjs'
import { formatTypes } from '../../utils/dictionary'
import { ImageService } from '../../services/image.service'
import { InfoStateService } from '../creative-info/info-state.service'
import { NotificationService } from '@app/core/services/notification/notification.service'
import { DigitalService } from '@app/core/services/no-auth/digital/digital.service'
import { CreativeStatus } from '@app/features/digital/simulacao-digital/digital-creative/pages/creative-details-page/model/creative-status.enum'
import { StatusDigitalCampaign } from '@app/features/digital/my-orders-digital/data-model/const-variable'
import { environment } from '@env/environment'
import { captureThumbnail } from '../../utils/creative-commom-functions'
import { AssetDrawerService } from '../../services/asset-drawer.service'

@Component({
  selector: 'app-creative-card',
  templateUrl: './creative-card.component.html',
})
export class CreativeCardComponent implements OnInit, OnDestroy {
  @Input() creative = new CreativeItem()
  @Input() disclaimerMessage: string
  @Input() disclaimerClass: string
  @Input() disclamerClass: string
  @Input() replaceCreative: boolean
  @Input() selected = false
  @Input() isAssociateCreative = false
  @Input() hiddenActions = false
  @Input() oppacityCard = false
  @Input() showCheck = true // exibição da opção de check
  @Input() isGallery = true // o componente está sendo chamado da galerida de criativo (true)   caso contrário (false)
  @Input() statusCampaign: string
  @Input() isReadyExhibition: boolean = false
  @Input() totalCreatives: number = 0

  @Output() onDeletedClick = new EventEmitter<CreativeItem>() // delete de criativo fora do escopo de galeria do criativo
  @Input() opacityCard = false
  @Input() positionItemCreative: string = ''
  @Input() isChangeAssociateCreative = false
  @Input() statusCampign: string

  @Output() onSelectClick = new EventEmitter<CreativeItem>()
  @Output() onReplaceCreative = new EventEmitter<CreativeItem>()
  @Output() onActivatedCreative = new EventEmitter<CreativeItem>() // ativação do criativo no ar
  @Output() onPauseCreative = new EventEmitter<CreativeItem>() // pausa do criativo no ar

  showActions = false
  subs = new Subscription()
  creativeThumbnail = `${environment.api.vaultBaseApi}/images/globo-no-thumb.png`
  menuItems = [
    {
      label: 'Ver mais informações',
      action: () => {
        this.infoStateService.setCreativeInfo(this.creative)
        this.assetDrawerService.openAssetModal('creative')
      },
      iconFilePath: '../../../../../assets/imgs/icons/error_outline.svg',
      iconSize: '14px',
      labelColor: '#616161',
    },
    {
      label: 'Excluir criativo',
      action: () => this.deletarCriativo(),
      iconFilePath: '../../../../../assets/imgs/icons/trash-bin-material.svg',
      iconSize: '14px',
      labelColor: '#DB082C',
      iconColor: '#DB082C',
    },
  ]
  constructor(
    private assetDrawerService: AssetDrawerService,
    private imageService: ImageService,
    private infoStateService: InfoStateService,
    private notificationService: NotificationService,
    private digitalService: DigitalService,
  ) {}

  ngOnInit() {
    this.fetchZipDetail()
    this.setMenuCondition()
  }
  ngOnDestroy(): void {
    this.subs.unsubscribe()
  }

  get formatTypes() {
    return formatTypes
  }

  fetchZipDetail() {
    if (this.creative.extension.endsWith('zip')) {
      this.digitalService.getZipDetail(this.creative.requestId).subscribe(
        res => {
          this.creativeThumbnail = res.content[0].s3Url
        },
        error => {
          console.error('Erro ao buscar detalhes do ZIP', error)
        },
      )
    } else if (
      this.creative.extension.endsWith('mp4') ||
      this.creative.extension.endsWith('mov') ||
      this.creative.extension.endsWith('avi')
    ) {
      captureThumbnail(this.creative.s3Url)
        .then(thumbnail => {
          this.creativeThumbnail = thumbnail
        })
        .catch(error => {
          console.error('Erro ao capturar a thumbnail:', error)
        })
    } else {
      this.creativeThumbnail = this.creative.s3Url
    }
  }

  deletarCriativo() {
    if (this.isGallery) {
      this.imageService
        .handleDelete(
          this.creative.name,
          [this.creative.requestId],
          this.isGallery,
        )
        .afterClosed()
        .subscribe(r => {
          if (r) {
            this.imageService.deletarCriativo(r)
          }
        })
    } else {
      this.deleteCreativeOutGallery()
    }
  }
  sendCreativeSelectEvent() {
    this.onSelectClick.emit(this.creative)
  }
  deleteCreativeOutGallery() {
    this.onDeletedClick.emit(this.creative)
  }
  replaceCreativeAction() {
    this.onReplaceCreative.emit(this.creative)
  }
  pauseCreative() {
    this.onPauseCreative.emit(this.creative)
  }
  continueCreative() {
    this.onActivatedCreative.emit(this.creative)
  }
  setMenuCondition() {
    if (this.replaceCreative && !this.isReadyExhibition) {
      this.menuItems = this.menuItems.filter(
        item => item.label !== 'Excluir criativo',
      )

      const substituirItem = {
        label: 'Substituir criativo',
        action: () => this.replaceCreativeAction(),
        iconFilePath: '../../../../../assets/imgs/icons/replace.svg',
        iconSize: '14px',
        labelColor: '#616161',
        iconColor: '#616161',
        labelGradient: true,
      }
      this.menuItems.splice(1, 0, substituirItem)
    }

    if (this.replaceCreative && this.isReadyExhibition) {
      // Substituir sempre disponível no caso de pronto para exibição
      const substituirItem = {
        label: 'Substituir criativo',
        action: () => this.replaceCreativeAction(),
        iconFilePath: '../../../../../assets/imgs/icons/replace.svg',
        iconSize: '14px',
        labelColor: '#616161',
        iconColor: '#616161',
        labelGradient: true,
      }

      const substituirIndex = this.menuItems.findIndex(
        item => item.label === 'Substituir criativo',
      )
      if (substituirIndex > -1) {
        this.menuItems[substituirIndex] = substituirItem
      } else {
        this.menuItems.splice(1, 0, substituirItem)
      }
    }

    if (!this.isGallery && !this.isReadyExhibition) {
      if (
        this.creative.status === CreativeStatus.CONTEUDO_PENDENTE ||
        this.creative.status === CreativeStatus.CONTEUDO_APROVADO
      ) {
        this.menuItems = this.menuItems.filter(
          item => item.label !== 'Excluir criativo',
        )
      }

      if (
        this.statusCampaign === StatusDigitalCampaign.ON_AIR &&
        this.creative.status === CreativeStatus.CONTEUDO_APROVADO &&
        this.creative.ativo === true
      ) {
        const creativePause = {
          label: 'Pausar exibição',
          action: () => this.pauseCreative(),
          iconFilePath: '../../../../../assets/imgs/icons/pause-creative.svg',
          iconSize: '14px',
          labelColor: '#616161',
          iconColor: '#616161',
        }
        this.menuItems.splice(1, 0, creativePause)
      }
      if (
        this.statusCampaign === StatusDigitalCampaign.ON_AIR &&
        this.creative.status === CreativeStatus.CONTEUDO_APROVADO &&
        this.creative.ativo === false
      ) {
        const creativeContinue = {
          label: 'Continuar exibição',
          action: () => this.continueCreative(),
          iconFilePath:
            '../../../../../assets/imgs/icons/continue-creative.svg',
          iconSize: '14px',
          labelColor: '#616161',
          iconColor: '#616161',
        }
        this.menuItems.splice(1, 0, creativeContinue)
      }
    }

    if (
      this.statusCampaign === StatusDigitalCampaign.ON_AIR &&
      this.totalCreatives <= 1
    ) {
      this.menuItems = this.menuItems.filter(
        item => item.label !== 'Excluir criativo',
      )
    }
  }
}
