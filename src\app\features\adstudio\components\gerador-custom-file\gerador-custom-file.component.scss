@import '_colors';
@import '_breakpoints';

.gerador-file-container {
  margin-top: 24px;

  border-radius: 12px;
  background: linear-gradient(
    90deg,
    #0079fd -37.91%,
    #2d51fb 20.11%,
    #5a29fa 78.13%,
    #8800f8 137.91%
  );
  width: 100%;
  padding: 29px 40px;

  .gerador-file-label {
    text-align: left;
    span {
      color: #ffffff85;
      font-size: 1rem;
    }

    p {
      color: #ffffff;
      font-size: 1.6rem;
      font-family: var(--Font-Family-Book, 'Inter Variable');
    }
  }

  .gerador-file-input {
    margin-left: 30px;
    width: 114px;
    height: 92px;
    border-radius: 12px;
    border: 4px solid #ffffff33;
    box-shadow: 0px 2px 8px 0px #1f11a01a;
    background-image: url('../../assets/icons/load-icon.svg');
    background-size: 75%;
    background-position: center;
    background-repeat: no-repeat;
    background-color: white;
    cursor: pointer;

    input {
      width: 100%;
      height: 100%;
      cursor: pointer;
      opacity: 0;
    }
  }
  .file-input-loading {
    background-image: url('../../assets/gifs/loading.gif') !important;
  }

  .file-controls {
    background-image: url('../../assets/images/file-control.svg');
    width: 40px;
    height: 40px;
    background-position: center;
    position: absolute;
    margin-top: 14%;
    margin-left: 74%;
    cursor: pointer;

    ul {
      background-color: white;
      margin-left: -170px;
      border-radius: 12px;
      position: relative;
      right: 40px;
      top: 5px;
      box-shadow: 10px 10px 16px 4px rgba(0, 0, 0, 0.2);

      li {
        display: flex;
        padding: 15px 20px;
        align-items: center;

        .icon {
          width: 18px;
          height: 18px;
          background-size: cover;
          margin-right: 10px;
          color: #61616178;

          &.change {
            background-image: url('../../assets/icons/change-image.svg');
          }

          &.remove {
            background-image: url('../../assets/icons/delete-image.png');
          }
        }

        &:nth-of-type(2) {
          border-top: 1px solid #6161613b;
        }
      }
    }
  }
}

.disclaimer {
  margin: 16px 0 0 0;
  background: var(--Color-Background-Secondary, #f7f7f7);
  border-radius: 12px;
  padding: 10px 20px;

  ngx-colors {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
  }

  p {
    font-size: 1rem;
    font-weight: 400;
    letter-spacing: -0.5%;
    line-height: 20px;
    color: #616161;

    b {
      color: black;
      font-weight: 400;
    }
  }
}

.hidden {
  transition: 0.2s;
  display: none;
}

@media (max-width: 768px) {
  .gerador-file-container {
    padding: 29px 20px;
    flex-direction: column;
    align-items: center;
    text-align: center;

    .gerador-file-label {
      text-align: center !important;

      p {
        text-align: center;
        margin-bottom: 1rem;
        font-size: 1.3rem;
      }

      .arrow {
        display: none;
      }
    }
    .gerador-file-input {
      margin: 0;
    }
    .file-controls {
      position: relative;
      margin-top: -13%;
      margin-left: 90%;
    }
  }
}
