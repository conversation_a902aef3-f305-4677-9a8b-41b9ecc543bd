import {
  Component,
  EventEmitter,
  Input,
  Output,
  SimpleChanges,
  OnInit,
  OnDestroy,
  ViewChild,
  ElementRef,
} from '@angular/core'
import { Router } from '@angular/router'
import { BytesPipe } from '@app/lib/bytes.pipe'
import { Subscription } from 'rxjs'
import { ImageService } from '../../services/image.service'
import { format } from 'date-fns'
import { AssetsItem } from '@app/core/services/no-auth/digital/model/digital-interface-utils'
import { InfoStateService } from '../creative-info/info-state.service'
import { AssetDrawerService } from '../../services/asset-drawer.service'

@Component({
  selector: 'app-asstes-info',
  templateUrl: './asstes-info.component.html',
  styleUrls: ['./asstes-info.component.scss'],
})
export class AsstesInfoComponent implements OnInit, OnDestroy {
  @Input() drawerOpen = false
  @Input() canDelete = true
  @Output() deleteAsset = new EventEmitter<AssetsItem>()
  @ViewChild('videoPlayer') videoPlayer: ElementRef<HTMLVideoElement>

  assetInfo: AssetsItem
  subs = new Subscription()
  bytesPipe = new BytesPipe()
  showPlayButton: boolean = true
  isLoading: boolean = false
  assetType: 'video' | 'image' | 'unknown' = 'unknown'
  assetUrl: string = ''

  daysUntilDeletion: number = 0
  expirationDate: Date | null = null

  constructor(
    private assetDrawerService: AssetDrawerService,
    private imageService: ImageService,
    private router: Router,
    private infoStateService: InfoStateService,
  ) {}

  ngOnInit() {
    this.subs.add(
      this.infoStateService.getAssetInfo$.subscribe(response => {
        this.assetInfo = response
        this.calculateExpirationDate()
      }),
    )
  }

  setAssetType(format: string) {
    const imageFormats = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'heic']
    const videoFormats = ['mp4', 'mov', 'avi', 'webm']

    const lowerFormat = format.toLowerCase()

    if (imageFormats.some(fmt => lowerFormat.includes(fmt))) {
      this.assetType = 'image'
    } else if (videoFormats.some(fmt => lowerFormat.includes(fmt))) {
      this.assetType = 'video'
    } else {
      this.assetType = 'unknown'
    }
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.drawerOpen && !changes.drawerOpen.currentValue) {
      this.resetState()
    }
  }

  resetState() {
    this.assetUrl = ''
    this.assetType = 'unknown'
  }

  ngOnDestroy(): void {
    this.subs.unsubscribe()
  }

  close() {
    this.assetDrawerService.closeAssetModal()
  }

  transformDimension(dimension: string) {
    return dimension ? dimension.replace(',', 'x') : 'Não disponível'
  }

  transformSize(size: number) {
    return size ? this.bytesPipe.transform(size) : 'Não disponível'
  }

  formattedDate(date: string) {
    if (!date) return 'Não disponível'
    try {
      return `${format(new Date(date), 'dd/MM/yyyy')} às ${format(new Date(date), 'HH:mm')}`
    } catch (error) {
      return date
    }
  }

  playVideo(videoElement: HTMLVideoElement) {
    videoElement.play()
    this.showPlayButton = false
  }

  onVideoPause() {
    this.showPlayButton = true
  }

  onVideoPlay() {
    this.showPlayButton = false
  }

  handleDelete() {
    this.close()
    this.deleteAsset.emit(this.assetInfo)
  }

  // TODO: REVER ESSE METODO PARA O CALCULO DA DATA VINDO DOP BACKEND
  calculateExpirationDate() {
    if (this.assetInfo?.favorite || !this.assetInfo?.date) {
      this.daysUntilDeletion = 0
      return null
    }

    const uploadDate = new Date(this.assetInfo.date)
    const expirationDate = new Date(uploadDate)
    expirationDate.setDate(uploadDate.getDate() + 30)

    const today = new Date()
    today.setHours(0, 0, 0, 0)

    const diffTime = expirationDate.getTime() - today.getTime()
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

    this.daysUntilDeletion = diffDays
    this.expirationDate = expirationDate

    return expirationDate
  }

  // Método para formatar a data de expiração
  formattedExpirationDate() {
    if (!this.expirationDate) {
      return 'Não disponível'
    }

    try {
      return format(this.expirationDate, 'dd/MM/yyyy')
    } catch (error) {
      return 'Data inválida'
    }
  }

  getExpirationMessage(): string {
    if (this.assetInfo?.favorite) {
      return ''
    }

    if (this.daysUntilDeletion > 0) {
      return this.daysUntilDeletion === 1
        ? '(1 dia restante)'
        : `(${this.daysUntilDeletion} dias restantes)`
    } else if (this.daysUntilDeletion === 0) {
      return '(Expira hoje)'
    } else {
      return this.daysUntilDeletion === -1
        ? '(Expirado há 1 dia)'
        : `(Expirado há ${-this.daysUntilDeletion} dias)`
    }
  }

  getExpirationColor(): string {
    if (this.assetInfo?.favorite) {
      return ''
    }

    if (this.daysUntilDeletion <= 7) {
      return 'text-red-600' // vermelho para 7 dias ou menos
    } else {
      return 'text-green-600' // verde para mais de 7 dias
    }
  }
}
