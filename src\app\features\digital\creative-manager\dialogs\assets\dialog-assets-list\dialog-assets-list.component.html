<div class="fixed inset-0 flex items-center justify-center z-50">
  <div
    class="rounded-lg overflow-hidden bg-white shadow-lg w-full max-w-7xl h-auto max-h-[90vh]"
  >
    <header
      class="border-b border-black/10 h-[70px] flex items-center justify-between p-4 lg:p-6 rounded-t-lg bg-white"
    >
      <div class="flex gap-3 items-center">
        <div
          class="truncate max-w-[220px] lg:max-w-full text-lg leading-7 font-semibold text-content-primary-light"
        >
          <span>Selecionar imagem</span>
        </div>
      </div>
    </header>

    <div class="bg-[#F7F7F7] h-full flex flex-col">
      <div class="px-6 pt-4">
        <div class="flex items-center justify-between mb-4">
          <p class="m-0">
            Selecione uma imagem para poder gerar o seu criativo:
          </p>
        </div>
      </div>

      <div
        class="px-6 pb-4 flex-grow overflow-y-auto custom-scrollbar"
        style="max-height: calc(90vh - 70px - 80px)"
      >
        <app-assets-images-list
          [isFlow4Selection]="isFlow4Selection"
          (onSelectAsset)="selectAsset($event)"
        ></app-assets-images-list>
      </div>

      <div
        class="flex justify-end gap-4 px-6 py-4 bg-white border-t border-black/10 sticky bottom-0"
      >
        <button
          [disabled]="countSelected === 0 || countSelected > 1"
          glbButton
          class="!uppercase bg-purple-500 text-white rounded py-2 px-4 transition hover:bg-purple-600"
          (click)="selectImage()"
        >
          SELECIONAR IMAGEM
        </button>
      </div>
    </div>
  </div>
</div>
