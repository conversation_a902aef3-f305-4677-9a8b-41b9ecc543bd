import {
  Component,
  ElementRef,
  EventEmitter,
  Input,
  OnInit,
  Output,
  ViewChild,
} from '@angular/core'
import { AssetsItem } from '@app/core/services/no-auth/digital/model/digital-interface-utils'
import { InfoStateService } from '../creative-info/info-state.service'
import { GeradorCriativosService } from '@app/features/adstudio/services/gerador-criativos.service'
import { AssetDrawerService } from '../../services/asset-drawer.service'
import { BytesPipe } from '@app/lib/bytes.pipe'

@Component({
  selector: 'app-assets-card',
  templateUrl: './assets-card.component.html',
  styleUrls: ['./assets-card.component.scss'],
})
export class AssetsCardComponent implements OnInit {
  @Input() image: AssetsItem
  @Input() canFavorite: boolean = true
  @Input() isFlow4Selection: boolean = false

  @Output() onSelectImage = new EventEmitter<void>()
  @Output() onFavoriteImage = new EventEmitter<void>()
  @Output() onMoreOptions = new EventEmitter<void>()
  @Output() onDeleteAsset = new EventEmitter<void>()

  @ViewChild('target') target!: ElementRef

  daysUntilDeletion: number = 0
  showActions: boolean = false

  // Propriedades para armazenar informações da imagem
  imageSize: number = 0
  imageDimensions: string = ''
  bytesPipe = new BytesPipe()

  private allMenuItems = [
    {
      label: 'Ver mais informações',
      action: () => {
        this.infoStateService.setAssetsInfo({
          ...this.image,
          dimension: this.imageDimensions || '1280x896',
          extension: 'PNG',
          size: this.imageSize || 0,
        })
        this.assetDrawerService.openAssetModal('asset')
      },
      iconFilePath: '../../../../../assets/imgs/icons/error_outline.svg',
      iconSize: '14px',
      labelColor: '#616161',
      fontSize: '13px',
    },
    {
      label: 'Usar em um novo criativo',
      action: () => this.useOnNewCreative(),
      iconFilePath: '../../../../../assets/imgs/icons/creator-magic.svg',
      iconSize: '18px',
      labelColor: '#616161',
      iconColor: '',
      marginRight: '5px',
      fontSize: '13px',
      hideInFlow4: true, // Ocultar no fluxo 4
    },
    {
      label: 'Excluir imagem',
      action: () => this.handleDeleteAsset(),
      iconFilePath: '../../../../../assets/imgs/icons/trash-bin-material.svg',
      iconSize: '14px',
      labelColor: '#DB082C',
      iconColor: '#DB082C',
      fontSize: '13px',
      hideInFlow4: true, // Ocultar no fluxo 4
    },
  ]

  get menuItems() {
    if (this.isFlow4Selection) {
      return this.allMenuItems.filter(item => !item.hideInFlow4)
    }
    return this.allMenuItems
  }

  showCheck: boolean = true
  showMenuOption: boolean = false
  hiddenActions: boolean = false
  isAssociateCreative: boolean = false
  opacityCard: boolean = false

  constructor(
    private assetDrawerService: AssetDrawerService,
    private infoStateService: InfoStateService,
    private geradorCriativosService: GeradorCriativosService,
  ) {}

  ngOnInit(): void {
    this.calculateDaysUntilDeletion()
    this.getImageInfo()
  }

  private getImageInfo(): void {
    if (!this.image?.src) {
      return
    }

    const img = new Image()

    img.onload = () => {
      this.imageDimensions = `${img.width}x${img.height}`
      this.getImageSizeInBytes(this.image.src)
    }

    img.onerror = () => {
      console.warn(
        'Erro ao carregar imagem para obter dimensões:',
        this.image.src,
      )
    }

    img.src = this.image.src
  }

  private getImageSizeInBytes(imageUrl: string): void {
    fetch(imageUrl, { method: 'HEAD' })
      .then(response => {
        if (response.ok) {
          const contentLength = response.headers.get('content-length')
          if (contentLength) {
            this.imageSize = parseInt(contentLength, 10)
          }
        }
      })
      .catch(error => {
        console.warn('Erro ao obter tamanho da imagem:', error)
      })
  }

  getFormattedImageSize(): string {
    return this.imageSize
      ? this.bytesPipe.transform(this.imageSize)
      : 'Não disponível'
  }

  get creativeThumbnail(): string {
    return this.image?.src || 'assets/default-image.jpg'
  }

  handleDeleteAsset(): void {
    this.onDeleteAsset.emit()
  }

  sendCreativeSelectEvent(): void {
    this.onSelectImage.emit()
  }

  onToggleFavorite(): void {
    if (this.canFavorite || this.image.favorite) {
      this.onFavoriteImage.emit()
    }
    this.shouldShowDeletionWarning()
  }

  onMoreOptionsClick(): void {
    this.showMenuOption = !this.showMenuOption
  }

  useOnNewCreative(): void {
    this.geradorCriativosService.handlePanelState(true)
    this.geradorCriativosService.setStepFormValues(5, {
      imageSelected: this.image,
    })
  }

  shouldShowDeletionWarning(): boolean {
    if (this.image.favorite) return false
    return this.daysUntilDeletion <= 7
  }

  calculateDaysUntilDeletion(): void {
    if (this.image.favorite) {
      this.daysUntilDeletion = 0
      return
    }

    if (!this.image.date) {
      this.daysUntilDeletion = 0
      return
    }

    try {
      const today = new Date()
      today.setHours(0, 0, 0, 0)
      let imageDate: Date

      // TODO: DESFAZZER ESSAS TRATATIVAS DAS BARRAS QUE FORAM FEITAS PARA O MOCK
      if (this.image.date.includes('/')) {
        const parts = this.image.date.split('/')
        if (parts.length !== 3)
          throw new Error(`Formato de data inválido: ${this.image.date}`)

        const day = parseInt(parts[0], 10)
        const month = parseInt(parts[1], 10) - 1
        const year = parseInt(parts[2], 10)
        imageDate = new Date(year, month, day)
      } else if (this.image.date.includes('-')) {
        const parts = this.image.date.split('-')
        if (parts.length !== 3)
          throw new Error(`Formato de data inválido: ${this.image.date}`)

        const year = parseInt(parts[0], 10)
        const month = parseInt(parts[1], 10) - 1
        const day = parseInt(parts[2], 10)

        if (isNaN(day) || isNaN(month) || isNaN(year))
          throw new Error(`Componentes de data inválidos: ${this.image.date}`)

        imageDate = new Date(year, month, day)
      } else {
        throw new Error(`Formato de data não reconhecido: ${this.image.date}`)
      }

      if (isNaN(imageDate.getTime())) {
        throw new Error(`Data inválida após conversão: ${this.image.date}`)
      }

      imageDate.setHours(0, 0, 0, 0)

      const deletionDate = new Date(imageDate)
      deletionDate.setDate(imageDate.getDate() + 30)

      const diffTime = deletionDate.getTime() - today.getTime()
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

      this.daysUntilDeletion = diffDays
    } catch (error) {
      console.error('Erro ao calcular dias até exclusão:', error)
      this.daysUntilDeletion = 0
    }
  }
}
