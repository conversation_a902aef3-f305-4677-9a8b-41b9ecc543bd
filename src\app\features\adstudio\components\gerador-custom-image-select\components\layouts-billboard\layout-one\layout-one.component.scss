.layout-container {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  height: 106px;
  max-height: 120px;
  font-family: Arial, sans-serif;
  background-color: #fdf6e3;
  width: 93%;
  box-sizing: border-box;
  position: relative;
  overflow: hidden;
}

.layout-content-text-logo {
  display: flex;
  flex-direction: row;
  align-items: center;
  flex: 1;
  padding: 0 16px;
}

.layout-text {
  font-size: 15px;
  width: 180px;
  font-weight: bold;
  color: #5a4634;
  line-height: 24px;
  height: 72px;
  display: flex;
  align-items: center;
  padding-left: 8px;
}

.layout-logo-container {
  display: flex;
  align-items: center;
  justify-content: center;
}

.layout-logo {
  width: 46px;
  height: 46px;
  border-radius: 8px;
  object-fit: contain;
}

.layout-content-detail {
  width: 6px;
  height: 100%;
  background-color: #f4a261;
  flex-shrink: 0;
}

.content-image-and-button {
  position: relative;
  width: 142px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  height: 100%;
}

.layout-data {
  width: 100%;
  height: 100%;
  object-fit: fill;
}

.layout-button {
  font-weight: bold;
  margin-top: 10px;
  color: #fff;
  background-color: #5a4634;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 65px;
  height: 18px;
  font-size: 8px;
  line-height: 9.68px;
  border: 1px solid white;
}

/* Responsividade */
@media (max-width: 768px) {
  .layout-container {
    flex-direction: row;
    height: auto;
    width: 95%;
  }

  .layout-text {
    font-size: 12px;
  }

  .layout-logo {
    width: 40px !important;
    height: 40px !important;
  }

  .layout-button {
    font-size: 10px;
  }
}

.billboard {
  width: 970px;
  height: 250px;

  .type {
    height: 250px;

    &_1 {
      height: 250px;
      display: flex;
      justify-content: space-between;

      .c-1 {
        height: 250px;
        display: flex;
        justify-content: space-around;
        align-items: flex-start;
        width: 617px;
        background-color: #fc560c;
      }

      .c-2 {
        width: 330px;
        display: flex;
        justify-content: flex-end;
        align-items: end;
        background-size: cover;
        background-position: center;
        background-repeat: no-repeat;

        .image {
          button {
            margin-bottom: 20px;
            margin-right: 24px;
            white-space: nowrap;
          }
        }
      }
    }
  }

  .logo {
    width: 120px;
    height: 120px;
    margin: 30px;
    border-radius: 24px;
    background-color: transparent;
    background-size: cover;
    background-position: center;
  }

  button {
    border-radius: 6px;
    border: 2px solid white;
    color: white;
    background-color: #ffffff;
    font-weight: 600;
    font-size: 1rem;
    margin-bottom: 20px;
    margin-right: 24px;
    white-space: nowrap;
    padding: 8px;
  }

  .image {
    background-size: cover;
  }

  .text {
    padding-left: 24px;
    p {
      display: flex;
      align-items: center;
      font-weight: 600;
      width: 420px;
      max-width: 420px;
      height: 162px;
      font-size: 40px;
      color: white;
    }
  }
}
@media (max-width: 480px) {
  .layout-container {
    flex-wrap: nowrap;
    height: 86px;
  }

  .layout-content-text-logo {
    gap: 8px !important;
    padding: 0 8px !important;
    max-width: 195px !important;
  }

  .layout-text {
    font-size: 12px !important;
    line-height: 14px !important;
    width: 130px;
  }

  .layout-logo {
    width: 30px !important;
    height: 30px !important;
  }

  .layout-button {
    font-size: 8px !important;
  }

  .container-logo {
    height: 30px !important;
    width: 30px !important;
    border-radius: 4px !important;
  }
  .layout-button {
    min-width: 75px !important;
  }

  .layout-logo-container-child {
    width: 40px !important;
    height: 40px !important;
  }
}
