export type AssetsFilter = {
  size: number
  page: number
  favorite?: boolean
  period?: string
  sort?: string
}

export type AssetResponse = {
  content: Asset[]
  pageable: {
    offset: number
    pageNumber: number
    pageSize: number
    paged: boolean
    unpaged: boolean
  }
  totalPages: number
  totalElements: number
  size: number
  number: number
  numberOfElements: number
  empty: boolean
  first: boolean
  last: boolean
}

export type Asset = {
  uuid: string
  name: string
  url: string
  additional_info: string
  created_date_utc: string
  is_favorite: boolean
  remaining_days_count: number
}
