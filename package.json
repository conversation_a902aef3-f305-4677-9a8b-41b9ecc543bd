{"name": "material-wh", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "start:dev": "ng serve --configuration=dev", "start:stg": "ng serve --configuration=stg", "start:prd": "ng serve --configuration=production", "build": "ng build", "build:stg": "ng build --configuration=stg && npm run sentry:sourcemaps:stg", "build:prod": "ng build --configuration=production && npm run sentry:sourcemaps:prod", "lint": "git diff --name-only | grep -E '\\.jsx?$|\\.tsx?$' | xargs eslint --fix", "lint:branch": "MAIN_BRANCH=$(git rev-parse --abbrev-ref origin/HEAD | sed 's|origin/||'); FILES=$(git diff --diff-filter=d --name-only $(git merge-base HEAD origin/$MAIN_BRANCH)...HEAD -- '*.ts' '*.html'); if [ -n \"$FILES\" ]; then echo \"$FILES\" | xargs eslint --fix --quiet; STATUS=$?; if [ $STATUS -eq 0 ]; then echo '<PERSON>t completed successfully with no errors or warnings.'; else echo 'Lint completed with warnings.'; fi; else echo 'No files to lint.'; fi", "format": "git diff --name-only | xargs -I {} sh -c 'prettier --write \"{}\"'", "pre-commit": "lint-staged --allow-empty", "test": "ng test --code-coverage", "test:ci": "CI=true ng test --no-watch --browsers=ChromeHeadlessNoSandbox --code-coverage", "prepare": "husky install", "postinstall": "husky install && ngcc --properties es2015 browser module main --first-only --create-ivy-entry-points", "generate-icons": "npx tsc --skipLibCheck src/app/core/services/custom-icon/generateImagePaths.ts && node src/app/core/services/custom-icon/generateImagePaths.js", "sentry:sourcemaps:stg": "sentry-cli sourcemaps inject --org gglobo --project globo-frontend-pme dist/ && sentry-cli --url https://sentry.dev.globoi.com/ sourcemaps upload --org gglobo --project globo-frontend-pme dist/ --rewrite", "sentry:sourcemaps:prod": "sentry-cli sourcemaps inject --org gglobo --project globo-frontend-pme dist/ && sentry-cli --url https://sentry.globoi.com/ sourcemaps upload --org gglobo --project globo-frontend-pme dist/ --rewrite"}, "private": true, "dependencies": {"@adtech/ui-kit": "^0.0.27", "@angular-devkit/core": "^15.2.10", "@angular/animations": "^15.2.10", "@angular/cdk": "^15.2.9", "@angular/cli": "^15.2.10", "@angular/common": "^15.2.10", "@angular/compiler": "^15.2.10", "@angular/core": "^15.2.10", "@angular/forms": "^15.2.10", "@angular/material": "^15.2.9", "@angular/platform-browser": "^15.2.10", "@angular/platform-browser-dynamic": "^15.2.10", "@angular/router": "^15.2.10", "@ngrx/store": "^10.0.0", "@ngrx/store-devtools": "^10.1.2", "@sentry/angular-ivy": "^7.114.0", "@sentry/cli": "^2.40.0", "@types/chart.js": "^2.9.34", "angularx-qrcode": "^10.0.11", "animate.css": "^4.1.1", "ansi-regex": "^3.0.1", "autoprefixer": "^10.4.20", "browser-image-compression": "^2.0.2", "chart.js": "^2.7.0", "chartjs-plugin-datalabels": "^0.7.0", "core-js": "^2.5.4", "date-fns": "^3.6.0", "decode-uri-component": "^0.2.1", "element-angular": "^0.7.6", "element-ui": "^2.15.14", "file-saver": "^2.0.0", "hammerjs": "^2.0.8", "hosted-git-info": "^3.0.8", "html2canvas": "1.3.4", "jsdom": "^16.5.2", "jsencrypt": "^3.3.2", "jszip": "^3.10.1", "lodash": "^4.17.21", "lodash-es": "^4.17.21", "moment": "2.29", "ng-click-outside": "^4.0.0", "ng2-charts": "^2.4.3", "ng5-slider": "^1.2.4", "ngrx-store-localstorage": "^10.1.0", "ngx-colors": "^3.6.0", "ngx-currency": "^3.0.0", "ngx-daterangepicker-material": "2.1.9", "ngx-image-cropper": "^7.2.0", "ngx-mask": "^10.0.4", "node-rsa": "^1.1.1", "npm": "^10.2.0", "path": "^0.12.7", "postcss": "^8.4.41", "query-string": "^6.13.6", "redux": "^3.7.2", "rxjs": "^6.0.0", "sass-loader": "^7.1.0", "semver": "7.5", "sweetalert2": "^11.6.13", "swiper": "^6.5.0", "tailwindcss": "^3.4.9", "tslib": "^2.0.0", "uuid": "^11.0.3", "valid-url": "^1.0.9", "xlsx": "^0.17.0", "zone.js": "~0.11.8"}, "devDependencies": {"@angular-builders/custom-webpack": "^19.0.1", "@angular-devkit/build-angular": "^15.2.10", "@angular-eslint/builder": "15.2.1", "@angular-eslint/eslint-plugin": "15.2.1", "@angular-eslint/eslint-plugin-template": "15.2.1", "@angular-eslint/schematics": "15.2.1", "@angular-eslint/template-parser": "15.2.1", "@angular/compiler-cli": "^15.2.10", "@angular/language-service": "^15.2.10", "@commitlint/config-conventional": "^18.4.3", "@eslint/js": "^9.4.0", "@ngneat/spectator": "^5.13.1", "@types/jasmine": "~3.6.0", "@types/jasminewd2": "~2.0.3", "@types/node": "^18.16.15", "@typescript-eslint/eslint-plugin": "5.48.2", "@typescript-eslint/parser": "5.48.2", "animated-tailwindcss": "^4.0.0", "cz-conventional-changelog": "^3.3.0", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-unused-imports": "^2.0.0", "globals": "^15.3.0", "husky": "^5.2.0", "jasmine": "~3.5.0", "jasmine-core": "~3.6.0", "jasmine-spec-reporter": "~5.0.0", "karma": "~6.4.2", "karma-chrome-launcher": "~3.1.0", "karma-coverage": "^2.2.1", "karma-coverage-istanbul-reporter": "~3.0.2", "karma-jasmine": "~4.0.0", "karma-jasmine-html-reporter": "^1.5.0", "lint-staged": "^15.2.7", "prettier": "3.3.1", "prettier-eslint": "^16.3.0", "process": "^0.11.10", "react": "^18.2.0", "react-dom": "^18.2.0", "sass": "^1.68.0", "storybook": "^7.6.1", "ts-node": "~5.0.1", "typescript": "~4.9.5", "typescript-eslint": "^7.12.0", "webpack": "^5.99.6"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "lint-staged": {"*.{ts}": ["eslint --fix", "git add"], "*.{ts,tsx,json,css,scss,md}": ["prettier --write"]}, "engines": {"node": "18.20.3"}}