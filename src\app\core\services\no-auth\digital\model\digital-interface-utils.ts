export interface CreativeListResponse {
  id: number
  name: string
  s3Url: string
  campaignUrl: string
  status: string
  agencyDocument: string
  customerDocument: string
  customerName: string
  facilId: string
  format: string
  extension: string
  dimension: string
  size: number
  requestId: string
  creationDate: string
  note: string
  zipDetail?: ZipDetail | Array<ZipDetail>
  creativeHistory?: Array<CreativeHistory>
  creativeUsage?: Array<CreativeUsageHistory>
  externalId?:string
}

export class ZipDetail {
  id: number
  zipName: string
  content: Content[]
  title: string | null
  subtitle: string | null
  actionButton: string | null
  status?: number

  constructor() {
    this.id = 0
    this.zipName = ''
    this.content = []
    this.title = null
    this.subtitle = null
    this.actionButton = null
  }
}

export class CreativeHistory {
  id: string
  type: EventData
  eventTime: string
  observation: string

  constructor() {
    this.id = ''
    this.type = new EventData()
    this.eventTime = ''
    this.observation = ''
  }
}

export class CreativeUsageHistory {
  idCampanha: string
  titulo: string
  dataCriacao: string
}

export class Content {
  id: number
  fileName: string
  s3Url: string
  requestId: string

  constructor() {
    this.id = 0
    this.fileName = ''
    this.s3Url = ''
    this.requestId = ''
  }
}

export enum CreativeStatus {
  PENDENTE = 'PENDENTE',
  APROVADO = 'APROVADO',
  REPROVADO = 'REPROVADO',
}

export interface CreativeSituation {
  status: CreativeStatus
}

export class CreativeItem {
  id: number
  name: string
  s3Url: string
  campaignUrl: string
  status: string
  agencyDocument: string
  customerDocument: string
  customerName: string
  facilId: string
  format: string
  extension: string
  dimension: string
  size: number
  externalId: string
  requestId: string
  creationDate: string
  note: string
  zipDetail: Array<ZipDetail>
  creativeHistory: Array<CreativeHistory>
  creativeUsage: Array<CreativeUsageHistory>
  lineItemId: string
  ativo: boolean
  geradoPorIA: boolean
  constructor(
    id?: number,
    name?: string,
    s3Url?: string,
    campaignUrl?: string,
    status?: string,
    agencyDocument?: string,
    customerDocument?: string,
    customerName?: string,
    facilId?: string,
    format?: string,
    extension?: string,
    dimension?: string,
    size?: number,
    requestId?: string,
    creationDate?: string,
    note?: string,
    zipDetail?: Array<ZipDetail>,
    creativeHistory?: Array<CreativeHistory>,
    creativeUsage?: Array<CreativeUsageHistory>,
    externalId?: string,
    lineItemId?: string,
    ativo?: boolean,
    geradoPorIA?: boolean,
  ) {
    this.id = id || 0
    this.name = name || ''
    this.s3Url = s3Url || ''
    this.campaignUrl = campaignUrl || ''
    this.status = status || ''
    this.agencyDocument = agencyDocument || ''
    this.customerDocument = customerDocument || ''
    this.customerName = customerName || ''
    this.facilId = facilId || ''
    this.format = format || ''
    this.extension = extension || ''
    this.dimension = dimension || ''
    this.size = size || 0
    this.requestId = requestId || ''
    this.creationDate = creationDate || new Date().toISOString()
    this.note = note || ''
    this.zipDetail = zipDetail || [new ZipDetail()]
    this.creativeHistory = creativeHistory || [new CreativeHistory()]
    this.creativeUsage = creativeUsage || [new CreativeUsageHistory()]
    this.externalId = externalId || ''
    this.lineItemId = lineItemId || ''
    this.ativo = ativo || null
    this.geradoPorIA = geradoPorIA  || null
  }
}

export enum CreativeEventType {
  CREATED = 'CREATED',
  UNDER_REVIEW = 'UNDER_REVIEW',
  APPROVED = 'APPROVED_CONTENT',
  APPROVED_TECH = 'APPROVED',
  REJECTED = 'REJECTED_CONTENT',
  ASSOCIATED = 'ASSOCIATED',
  ERROR = 'ERROR',
}

export class EventData {
  eventName: string
  title: string
  description: string

  constructor() {
    this.eventName = ''
    this.title = ''
    this.description = ''
  }
}

export interface AssetsItem {
  id: string
  name: string
  date: string
  src: string
  selected: boolean
  favorite: boolean
  format?: string
  specialSelection?: boolean
  extension?: string
  dimension?: string
  size?: number
  type?: string
  uuid: string
}

export interface InfoItem {
  type: 'creative' | 'asset'
  data: CreativeItem | AssetsItem
}

export interface AssetRequest {
  name: string
  qtd_pessoas: number
  filtro: string
  url: string
}
