import { ComponentFixture, TestBed } from '@angular/core/testing'

import { DialogGenericAssetsComponent } from './dialog-generic-assets.component'

describe('DialogGenericAssetsComponent', () => {
  let component: DialogGenericAssetsComponent
  let fixture: ComponentFixture<DialogGenericAssetsComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [DialogGenericAssetsComponent],
    }).compileComponents()

    fixture = TestBed.createComponent(DialogGenericAssetsComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
