/* eslint-disable @typescript-eslint/no-explicit-any */
import { Injectable } from '@angular/core'
import { BehaviorSubject, Observable } from 'rxjs'
import { GeradorFormModel } from '../models/gerador-form.model'
import { map } from 'rxjs/operators'

@Injectable({
  providedIn: 'root',
})
export class GeradorCriativosService {
  private _isOpen: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(
    false,
  )

  private notify: BehaviorSubject<number> = new BehaviorSubject(0)
  private lastStep: BehaviorSubject<number> = new BehaviorSubject(0)
  private steps: GeradorFormModel[] = [
    {
      active: false,
      step: 1,
      valid: true,
      controls: ['layout'],
      component: {
        title: 'Formato',
        subtitle: 'Escolha um formato',
        step_title: 'Próximo',
        step_icon: 'next-arrow-white.svg',
        can_back: true,
      },
    },
    {
      active: false,
      step: 2,
      valid: false,
      controls: ['name', 'type', 'objective', 'logo', 'saveInfo', 'detail'],
      component: {
        title: 'Sobre o seu negócio',
        subtitle: 'Informações sobre o seu negócio',
        step_title: 'Próximo',
        step_icon: 'next-arrow-white.svg',
        can_back: true,
      },
    },
    {
      active: true,
      step: 3,
      valid: true,
      controls: [
        'mainText',
        'ctaText',
        'layoutFirstColor',
        'layoutSecondColor',
        'layoutThirdColor',
      ],
      component: {
        title: 'Texto e customização',
        subtitle: 'Texto do seu criativo',
        step_title: 'Próximo',
        step_icon: 'next-arrow-white.svg',
        can_back: true,
      },
    },
    {
      active: false,
      step: 4,
      valid: false,
      controls: [
        'imageType',
        'imageBackgroundType',
        'peopleNumber',
        'imageDescription',
        'obDescription',
        'imageFirstColor',
        'imageSecondColor',
        'imageThirdColor',
      ],
      component: {
        title: 'Imagem',
        subtitle: 'Gerador de imagem',
        step_title: 'Gerar imagem',
        step_title_short: 'Gerar',
        step_icon: 'magic-icon-white.svg',
        can_back: true,
      },
    },
    {
      active: false,
      step: 5,
      valid: false,
      controls: ['imageSelected'],
      component: {
        title: 'Finalização',
        subtitle: 'Escolha uma das imagens',
        step_title: 'Gerar Criativos',
        step_icon: null,
        can_back: true,
      },
    },
    {
      active: false,
      step: 6,
      valid: false,
      controls: ['layoutSelected'],
      component: {
        title: 'Finalização',
        subtitle: 'Escolha um criativo',
        step_icon: null,
        step_title: 'CONCLUÍDO',
        step_title_short: 'Salvar',
        can_back: true,
      },
    },
  ]
  private usingGalleryImage: BehaviorSubject<boolean> = new BehaviorSubject(
    false,
  )

  private currentStep: BehaviorSubject<GeradorFormModel> = new BehaviorSubject(
    this.steps.find(el => (el.active = true)),
  )

  private clearRequest = new BehaviorSubject<boolean>(false)

  private formValues = new BehaviorSubject<Record<number, any>>({})

  handleUsingGalleryImage(using: boolean): void {
    this.usingGalleryImage.next(using)
  }

  getUsingGalleryImage(): Observable<boolean> {
    return this.usingGalleryImage.asObservable()
  }

  handlePanelState(isOpen: boolean): void {
    return this._isOpen.next(isOpen)
  }

  getPanelState(): Observable<boolean> {
    return this._isOpen.asObservable()
  }

  getCurrentStep(): Observable<GeradorFormModel> {
    return this.currentStep.asObservable()
  }

  getLastStep(): Observable<number> {
    return this.lastStep.asObservable()
  }

  setCurrentStep(idCurrent: number, lastStep?: number): void {
    this.lastStep.next(lastStep)
    const nextIdPage = idCurrent + 1

    if (nextIdPage > this.steps.length) {
      this.handlePanelState(false)
      this.currentStep.next(this.steps[0])
    } else {
      const newStep = this.steps.find(el => el.step === nextIdPage)
      this.currentStep.next(newStep)
    }

    this.notifyStep(nextIdPage)
  }

  getNotification(): Observable<number> {
    return this.notify.asObservable()
  }

  listenClearRequest(): Observable<boolean> {
    return this.clearRequest.asObservable()
  }

  sendClearRequest(): void {
    this.clearRequest.next(true)
    this.clearAllFormData()
  }

  notifyStep(nextIdPage: number): void {
    this.notify.next(nextIdPage)
  }

  updateCurrentStep(valid: boolean) {
    const updatedStep: GeradorFormModel = {
      ...this.currentStep.value,
      valid: valid,
    }
    this.currentStep.next(updatedStep)
  }
  getStepByNumber(stepNumber: number): GeradorFormModel | undefined {
    return this.steps.find(step => step.step === stepNumber)
  }
  setStepFormValues(stepNumber: number, values: any): void {
    const updatedValues = {
      ...this.formValues.value,
      [stepNumber]: { ...values },
    }
    this.formValues.next(updatedValues)
  }

  getStepFormValues(stepNumber: number): any {
    return this.formValues.value[stepNumber]
  }
  setStepsInvalidFromSecond(): void {
    this.steps = this.steps.map(step => ({
      ...step,
      valid: step.step === 1 || step.step === 4 ? step.valid : false,
    }))

    const updatedCurrentStep = this.steps.find(
      step => step.step === this.currentStep.value?.step,
    )
    if (updatedCurrentStep) {
      this.currentStep.next({ ...updatedCurrentStep })
    }
  }

  clearAllFormData(): void {
    this.formValues.next({})

    this.steps = this.steps.map(step => ({
      ...step,
      valid: step.step === 1 || step.step === 4 ? step.valid : false,
    }))
    this.steps.forEach(step => {
      step.controls.forEach(control => {
        const formControl = this.getStepFormValues(step.step)?.[control]
        if (formControl) {
          formControl.setValue('')
          formControl.markAsTouched()
          formControl.updateValueAndValidity()
        }
      })
    })
    this.currentStep.next(this.steps[0])
  }

  getStepDataObservable(stepNumber: number): Observable<any> {
    return this.formValues
      .asObservable()
      .pipe(map(formValues => formValues[stepNumber] || {}))
  }
}
