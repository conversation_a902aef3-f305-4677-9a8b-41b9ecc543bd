import { Injectable } from '@angular/core'
import { environment } from '@env/environment'
import { Observable } from 'rxjs'
import {
  GeradorTextoRequest,
  GeradorTextoResponse,
} from '../models/gerador-texto.model'
import { HttpClient } from '@angular/common/http'
import {
  GeradorImageRequest,
  GeradorImageResponse,
} from '../models/gerador-image.model'
import { IGenerateCreativeRequest } from '../models/criativo-request.model'

@Injectable({
  providedIn: 'root',
})
export class GeradorCriativosHubService {
  constructor(private http: HttpClient) {}

  generateTextCampaign(
    request: GeradorTextoRequest,
  ): Observable<GeradorTextoResponse> {
    const apiUrl = environment.api.adstudio.getGeneratedText
    return this.http.post<GeradorTextoResponse>(apiUrl, request)
  }

  generateImageCampaign(
    request: GeradorImageRequest,
  ): Observable<GeradorImageResponse[]> {
    const apiUrl = environment.api.adstudio.getGeneratedImage
    return this.http.post<GeradorImageResponse[]>(apiUrl, request)
  }

  generateCreative(
    request: IGenerateCreativeRequest,
  ): Observable<GeradorImageResponse[]> {
    const apiUrl = environment.api.postGenerateCreative
    return this.http.post<GeradorImageResponse[]>(apiUrl, request)
  }

  postFile(request: FormData): Observable<GeradorImageResponse[]> {
    const apiUrl = environment.api.postSaveFile
    return this.http.post<GeradorImageResponse[]>(apiUrl, request)
  }
}
