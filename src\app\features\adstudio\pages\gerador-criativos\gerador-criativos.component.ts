import { Subscription } from 'rxjs'
import { FooterComponent } from '../../components/gerador-footer/gerador-footer.component'
import { FooterActionService } from '../../services/footer-action.service'
import { GeradorCriativosService } from '../../services/gerador-criativos.service'
import {
  Component,
  EventEmitter,
  OnDestroy,
  OnInit,
  Output,
  ViewChild,
} from '@angular/core'

@Component({
  selector: 'app-gerador-criativos',
  templateUrl: './gerador-criativos.component.html',
  styleUrls: ['./gerador-criativos.component.scss'],
})
export class GeradorCriativosComponent implements OnInit, OnDestroy {
  isOpen: boolean = true
  termosAceito: boolean = false
  isFooterVisible: boolean = true
  private subscription: Subscription = new Subscription()

  @ViewChild('footer') footerComponent: FooterComponent

  constructor(
    private service: GeradorCriativosService,
    private actionFooter: FooterActionService,
  ) {
    this.termosAceito = false
  }
  ngOnDestroy(): void {
    this.subscription.unsubscribe()
  }
  @Output() imageGenerated = new EventEmitter<string>()

  ngOnInit(): void {
    this.service.getPanelState().subscribe(isOpen => {
      this.isOpen = isOpen
      if (!isOpen) {
        this.termosAceito = false
      }
    })

    this.subscription.add(
      this.actionFooter.footerVisibility$.subscribe(visible => {
        this.isFooterVisible = visible
      }),
    )
  }
  onImageGenerated(image: string) {
    this.imageGenerated.emit(image)
  }
  closeSidebar() {
    this.service.handlePanelState(false)
  }

  handleAceite(aceito: boolean) {
    this.termosAceito = aceito

    if (!aceito) {
      this.closeSidebar()
    }
  }
}
