import { Component, OnInit, OnD<PERSON>roy, Input } from '@angular/core'
import { GeradorFormModel } from '../../models/gerador-form.model'
import { GeradorCriativosService } from '../../services/gerador-criativos.service'
import { Subscription } from 'rxjs'
import { FooterActionService } from '../../services/footer-action.service'

@Component({
  selector: 'app-gerador-footer',
  templateUrl: './gerador-footer.component.html',
  styleUrls: ['./gerador-footer.component.scss'],
})
export class FooterComponent implements OnInit, OnDestroy {
  currentStep: GeradorFormModel
  @Input() isVisible: boolean = true
  private stepSubscription: Subscription
  private visibilitySubscription: Subscription

  constructor(
    private readonly service: GeradorCriativosService,
    private actionFooter: FooterActionService,
  ) {}

  ngOnInit(): void {
    this.stepSubscription = this.service.getCurrentStep().subscribe(step => {
      this.currentStep = step
    })

    this.visibilitySubscription = this.actionFooter.footerVisibility$.subscribe(
      isVisible => {
        this.isVisible = isVisible
      },
    )
  }

  ngOnDestroy(): void {
    this.service.setStepsInvalidFromSecond()
    if (this.stepSubscription) {
      this.stepSubscription.unsubscribe()
    }
    if (this.visibilitySubscription) {
      this.visibilitySubscription.unsubscribe()
    }
  }

  handleNextStep(id: number): void {
    this.service.setCurrentStep(id)
    this.actionFooter.emitEvent('confirmar', id)
  }

  handleBackStep(id: number): void {
    this.service.setCurrentStep(id - 2)
    this.actionFooter.emitEvent('voltar', id)
  }
}
