import { Injectable } from '@angular/core'
import { BehaviorSubject, Observable } from 'rxjs'
import { map } from 'rxjs/operators'

export type AssetModalType = 'creative' | 'asset' | null

@Injectable({
  providedIn: 'root',
})
export class AssetDrawerService {
  private assetModalStateSubject = new BehaviorSubject<{
    open: boolean
    type: AssetModalType
  }>({ open: false, type: null })

  private cardToggleSubject = new BehaviorSubject<boolean>(false)

  openAssetModal(type: AssetModalType) {
    this.assetModalStateSubject.next({ open: true, type })
    this.cardToggleSubject.next(true) // Abre o drawer
  }

  closeAssetModal() {
    this.assetModalStateSubject.next({ open: false, type: null })
    this.cardToggleSubject.next(false)
  }

  get cardToggleSubject$(): Observable<boolean> {
    return this.cardToggleSubject.asObservable()
  }

  get isAssetModalOpen$(): Observable<boolean> {
    return this.assetModalStateSubject.pipe(
      map(state => state.open && state.type === 'asset'),
    )
  }

  get isCreativeModalOpen$(): Observable<boolean> {
    return this.assetModalStateSubject.pipe(
      map(state => state.open && state.type === 'creative'),
    )
  }
  get assetModalState$(): Observable<{ open: boolean; type: AssetModalType }> {
    return this.assetModalStateSubject.asObservable()
  }
}
