import { SharedModule } from '../../../../../../../shared/shared.module'
import { NgModule } from '@angular/core'
import { CommonModule } from '@angular/common'
import { GloboPagesPreviewDesktopComponent } from './globo-pages-preview-desktop.component'
import { PreviewImageStructureModule } from '../preview-image-structure/preview-image-structure.module'
import { SkeletonPreviewFakeModule } from '../skeleton-preview-fake/skeleton-preview-fake.module'
import { PreviewImagePublicityModule } from '../../preview-image-publicity/preview-image-publicity.modules'
import { PreviewImageCarrosselModule } from '../../preview-image-carrossel/preview-image-carrossel.module'
import { AdstudioModule } from '@app/features/adstudio/adstudio.module'

@NgModule({
  declarations: [GloboPagesPreviewDesktopComponent],
  imports: [
    CommonModule,
    PreviewImageStructureModule,
    SharedModule,
    PreviewImagePublicityModule,
    SkeletonPreviewFakeModule,
    PreviewImageCarrosselModule,
    AdstudioModule,
  ],
  exports: [GloboPagesPreviewDesktopComponent],
})
export class GloboPagesPreviewDesktopModule {}
