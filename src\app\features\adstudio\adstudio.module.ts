import {
  CUSTOM_ELEMENTS_SCHEMA,
  NgModule,
  NO_ERRORS_SCHEMA,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { GeradorCriativosComponent } from './pages/gerador-criativos/gerador-criativos.component'
import { MatRippleModule } from '@angular/material/core'
import { ReactiveFormsModule } from '@angular/forms'
import { SharedModule } from '@app/shared/shared.module'
import { GeradorCriativosService } from './services/gerador-criativos.service'
import { HeaderComponent } from './components/gerador-header/gerador-header.component'
import { FooterComponent } from './components/gerador-footer/gerador-footer.component'
import { GeradorFormComponent } from './components/gerador-form/gerador-form.component'
import { GeradorCustomSelectComponent } from './components/gerador-custom-select/gerador-custom-select.component'
import { GeradorCustomInputComponent } from './components/gerador-custom-input/gerador-custom-input.component'
import { GeradorCustomFileComponent } from './components/gerador-custom-file/gerador-custom-file.component'
import { GeradorCustomTextGeneratorComponent } from './components/gerador-custom-text-generator/gerador-custom-text-generator.component'
import { GeradorPreviewComponent } from './components/gerador-preview/gerador-preview.component'
import { GeradorColorPalleteComponent } from './components/gerador-color-pallete/gerador-color-pallete.component'
import { GeradorCollorPickerComponent } from './components/gerador-collor-picker/gerador-collor-picker.component'
import { GeradorCardToggleComponent } from './components/gerador-card-toggle/gerador-card-toggle.component'
import { GeradorCustomImageSelectComponent } from './components/gerador-custom-image-select/gerador-custom-image-select.component'
import { GeradorDisclaimerComponent } from './components/gerador-disclaimer/gerador-disclaimer.component'
import { GeradorButtonsComponent } from './components/gerador-buttons/gerador-buttons.component'
import { ColorService } from './services/color.service'
import { GeradorTermosUsoComponent } from './components/gerador-termos-uso/gerador-termos-uso.component'
import { DialogHelper } from '@app/shared/utils/dialog-helper'
import { DialogErrorComponent } from './dialogs/dialog-error/dialog-error.component'
import { DialogConfirmComponent } from './dialogs/dialog-confirm/dialog-confirm.component'
import '@adtech/ui-kit/webcomponents'
import { RetanguloMedioLayoutOneComponent } from './components/gerador-custom-image-select/components/layout-retangulo-medio/layout-one/layout-one.component'
import { RetanguloMedioLayoutTwoComponent } from './components/gerador-custom-image-select/components/layout-retangulo-medio/layout-two/layout-two.component'
import { BillboardLayoutOneComponent } from './components/gerador-custom-image-select/components/layouts-billboard/layout-one/layout-one.component'
import { BillboardLayoutThreeComponent } from './components/gerador-custom-image-select/components/layouts-billboard/layout-three/layout-three.component'
import { BillboardLayoutTwoComponent } from './components/gerador-custom-image-select/components/layouts-billboard/layout-two/layout-two.component'
import { HalfpageLayoutOneComponent } from './components/gerador-custom-image-select/components/layouts-half-page/layout-one/layout-one.component'
import { HalfpageLayoutThreeComponent } from './components/gerador-custom-image-select/components/layouts-half-page/layout-three/layout-three.component'
import { HalfpageLayoutTwoComponent } from './components/gerador-custom-image-select/components/layouts-half-page/layout-two/layout-two.component'
import { MaxiboardLayoutOneComponent } from './components/gerador-custom-image-select/components/layouts-maxiboard/layout-one/layout-one.component'
import { MaxiboardLayoutThreeComponent } from './components/gerador-custom-image-select/components/layouts-maxiboard/layout-three/layout-three.component'
import { MaxiboardLayoutTwoComponent } from './components/gerador-custom-image-select/components/layouts-maxiboard/layout-two/layout-two.component'
import { SuperLeaderboardLayoutOneComponent } from './components/gerador-custom-image-select/components/layouts-super-leaderboard/layout-one/layout-one.component'
import { SuperLeaderboardLayoutThreeComponent } from './components/gerador-custom-image-select/components/layouts-super-leaderboard/layout-three/layout-three.component'
import { SuperLeaderboardLayoutTwoComponent } from './components/gerador-custom-image-select/components/layouts-super-leaderboard/layout-two/layout-two.component'
import { WatterMarkComponent } from './components/watter-mark/watter-mark.component'
import { OptionCardComponent } from './components/option-card/option-card.component'
import { SkeletonLoaderComponent } from './components/skeleton-loader/skeleton-loader.component'

@NgModule({
  declarations: [
    GeradorCriativosComponent,
    HeaderComponent,
    FooterComponent,
    GeradorFormComponent,
    GeradorCustomSelectComponent,
    GeradorCustomInputComponent,
    GeradorCustomFileComponent,
    GeradorCustomTextGeneratorComponent,
    GeradorPreviewComponent,
    GeradorColorPalleteComponent,
    GeradorCollorPickerComponent,
    GeradorCardToggleComponent,
    GeradorCustomImageSelectComponent,
    GeradorDisclaimerComponent,
    GeradorButtonsComponent,
    DialogErrorComponent,
    DialogConfirmComponent,
    GeradorTermosUsoComponent,
    BillboardLayoutOneComponent,
    BillboardLayoutTwoComponent,
    BillboardLayoutThreeComponent,
    RetanguloMedioLayoutOneComponent,
    RetanguloMedioLayoutTwoComponent,
    HalfpageLayoutOneComponent,
    HalfpageLayoutTwoComponent,
    HalfpageLayoutThreeComponent,
    MaxiboardLayoutTwoComponent,
    MaxiboardLayoutThreeComponent,
    MaxiboardLayoutOneComponent,
    SuperLeaderboardLayoutOneComponent,
    SuperLeaderboardLayoutTwoComponent,
    SuperLeaderboardLayoutThreeComponent,
    WatterMarkComponent,
    OptionCardComponent,
    SkeletonLoaderComponent,
  ],
  imports: [CommonModule, SharedModule, MatRippleModule, ReactiveFormsModule],
  providers: [GeradorCriativosService, ColorService, DialogHelper],
  exports: [
    GeradorCriativosComponent,
    BillboardLayoutOneComponent,
    BillboardLayoutThreeComponent,
    BillboardLayoutTwoComponent,
    RetanguloMedioLayoutOneComponent,
    RetanguloMedioLayoutTwoComponent,
    HalfpageLayoutOneComponent,
    HalfpageLayoutTwoComponent,
    HalfpageLayoutThreeComponent,
    MaxiboardLayoutTwoComponent,
    MaxiboardLayoutThreeComponent,
    MaxiboardLayoutOneComponent,
    SuperLeaderboardLayoutOneComponent,
    SuperLeaderboardLayoutTwoComponent,
    SuperLeaderboardLayoutThreeComponent,
    WatterMarkComponent,
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA],
})
export class AdstudioModule {}
