import { Component, EventEmitter, Input, Output } from '@angular/core'

@Component({
  selector: 'app-option-card',
  templateUrl: './option-card.component.html',
  styleUrls: ['./option-card.component.scss'],
})
export class OptionCardComponent {
  @Input() title!: string
  @Input() description!: string
  @Input() isSelected: boolean = false
  @Input() iconSrc: string
  @Output() optionClick = new EventEmitter<void>()

  onClick() {
    this.optionClick.emit()
  }
}
