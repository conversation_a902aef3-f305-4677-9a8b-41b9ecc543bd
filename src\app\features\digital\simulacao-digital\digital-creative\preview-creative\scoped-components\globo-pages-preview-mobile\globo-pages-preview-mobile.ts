import { GloboPagesPreviewDesktopModule } from './../globo-pages-preview-desktop/globo-pages-preview-desktop.module'
import { NgModule } from '@angular/core'
import { CommonModule } from '@angular/common'
import { GloboPagesPreviewMobileComponent } from './globo-pages-preview-mobile.component'
import { SharedModule } from '@app/shared/shared.module'
import { PreviewImageStructureModule } from '../preview-image-structure/preview-image-structure.module'
import { SkeletonPreviewFakeModule } from '../skeleton-preview-fake/skeleton-preview-fake.module'
import { PreviewImagePublicityModule } from '../../preview-image-publicity/preview-image-publicity.modules'
import { PreviewImageCarrosselModule } from '../../preview-image-carrossel/preview-image-carrossel.module'

@NgModule({
  declarations: [GloboPagesPreviewMobileComponent],
  imports: [
    CommonModule,
    SharedModule,
    PreviewImageStructureModule,
    PreviewImagePublicityModule,
    GloboPagesPreviewDesktopModule,
    SkeletonPreviewFakeModule,
    PreviewImageCarrosselModule,
  ],
  exports: [GloboPagesPreviewMobileComponent],
})
export class GloboPagesPreviewMobileModule {}
