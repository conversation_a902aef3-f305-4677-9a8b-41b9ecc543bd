import { Injectable } from '@angular/core'
import { MatDialogConfig } from '@angular/material/dialog'
import { environment } from '@env/environment'
import { Asset, AssetResponse, AssetsFilter } from '../model/assets.model'
import { BehaviorSubject, Observable } from 'rxjs'
import { HttpClient } from '@angular/common/http'
import { NotificationService } from '@app/core/services/notification/notification.service'
import { AssetsItem } from '@app/core/services/no-auth/digital/model/digital-interface-utils'
import { sign } from 'crypto'
import { map } from 'rxjs/operators'

@Injectable({
  providedIn: 'root',
})
export class AssetsItemService {
  dialogConfig = new MatDialogConfig()
  private urlGallery = environment.api.adstudio
  public assetStore = new BehaviorSubject<AssetsItem[]>([])
  private totalImagesSubject = new BehaviorSubject<number>(0)
  public totalImages$ = this.totalImagesSubject.asObservable()
  private initialFilter = {
    size: 10,
    page: 0,
    favorite: false,
    period: '',
  }
  images: AssetsItem[] = []

  constructor(
    private http: HttpClient,
    private notificationService: NotificationService,
  ) {}

  getAssets(filter: AssetsFilter): Observable<AssetsItem[]> {
    let url = environment.api.adstudio.listAssets
      .replace('{size}', filter.size.toFixed())
      .replace('{page}', filter.page.toFixed())
      .replace('{sort}', filter.sort)

    if (filter.favorite) {
      url += '&favorite=true'
    }
    if (filter.period.length > 0) {
      url += '&period=' + filter.period
    }

    this.http.get<AssetResponse>(url).subscribe(
      (response: AssetResponse) => {
        this.updateImageList(response?.content, response?.totalElements || 0)
      },
      erro => {
        console.error('Erro ao listar criativos: ' + erro)
      },
    )
    return this.assetStore.asObservable()
  }

  getFavorriteAssets(): Observable<any> {
    const filter = {
      size: 0,
      page: 0,
      sort: 'date_desc',
      favorite: true,
    }
    return this.http
      .get<AssetResponse>(
        environment.api.adstudio.listAssets
          .replace('{size}', filter.size.toFixed())
          .replace('{page}', filter.page.toFixed())
          .replace('{sort}', filter.sort) + '&favorite=true',
      )
      .pipe(map(response => response.totalElements))
  }

  deleteAsset(assetList: string[]) {
    return this.http
      .delete<string>(this.urlGallery.baseAssets, {
        body: assetList,
      })
      .subscribe(
        _ => {
          this.notificationService.showToastSuccess(
            'Imagen(s) excluídas com sucesso!',
          )
          this.getAssets(this.initialFilter).subscribe()
        },
        err => console.error('Erro ao excluir imagem: ' + err),
      )
  }

  private updateImageList(newList: Asset[], totalImagesValue: number): void {
    this.images = []
    newList.forEach((asset: Asset) => {
      this.images.push({
        date: this.formatDate(asset.created_date_utc),
        id: asset.uuid,
        name: asset.name,
        src: asset.url,
        selected: false,
        favorite: asset.is_favorite,
        uuid: asset.uuid,
        type: 'image',
      })
    })
    this.assetStore.next(this.images)
    this.totalImagesSubject.next(totalImagesValue)
  }

  private formatDate(date): string {
    const fullDate = new Date(date)
    const year = fullDate.getFullYear()
    const month = String(fullDate.getMonth() + 1).padStart(2, '0')
    const day = String(fullDate.getDate()).padStart(2, '0')
    return `${year}-${month}-${day}`
  }
}
