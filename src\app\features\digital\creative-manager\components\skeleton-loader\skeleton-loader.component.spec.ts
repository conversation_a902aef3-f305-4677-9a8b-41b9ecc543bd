import { ComponentFixture, TestBed } from '@angular/core/testing'
import { NO_ERRORS_SCHEMA } from '@angular/core'

import { SkeletonLoaderComponent } from './skeleton-loader.component'

describe('SkeletonLoaderComponent', () => {
  let component: SkeletonLoaderComponent
  let fixture: ComponentFixture<SkeletonLoaderComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      schemas: [NO_ERRORS_SCHEMA],
      declarations: [SkeletonLoaderComponent],
    }).compileComponents()

    fixture = TestBed.createComponent(SkeletonLoaderComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
