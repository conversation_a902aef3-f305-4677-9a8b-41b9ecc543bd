.container-c-dialog {
  padding: 24px;
  height: 100vh;
}
.c-dialog-termos {
  h4 {
    font-size: 1.8rem;
    margin: 24px 0 20px 0;
    font-family: var(--Font-Family-Display, 'Inter Variable');
    color: #1c1c1c;
  }
  p {
    font-size: 1rem;
    font-family: var(--Font-Family-Book, 'Inter Variable');
    color: #616161;
  }

  .image {
    min-width: 328px;
    height: 220px;
    width: 100%;
    margin: 0 auto;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    background-image: url('../../assets/images/termos-uso-banner.svg');
  }
}
.c-dialog-body {
  overflow-y: auto;
  margin-bottom: 100px;
  padding-right: 10px;
}

.c-dialog-footer {
  height: 120px;
  position: absolute;
  bottom: 40px;
  right: 20px;
  width: 92%;
}
.check-container {
  margin: 20px 0;
  .check {
    width: 20px;
    height: 20px;
    background-image: url('../../assets/icons/icon-unchecked.svg');

    &.checked {
      background-image: url('../../assets/icons/icon-checked.svg');
    }
  }
}

::ng-deep .el-checkbox__input.is-checked .el-checkbox__inner {
  background: linear-gradient(
    90deg,
    #0079fd 25%,
    #2d51fb 50%,
    #5a29fa 75%,
    #8800f8 100%
  );
  border-color: transparent;
}

::ng-deep .el-checkbox__input.is-checked .el-checkbox__inner:hover {
  background: linear-gradient(
    90deg,
    #0079fd 25%,
    #2d51fb 50%,
    #5a29fa 75%,
    #8800f8 100%
  );
}

::ng-deep .el-checkbox__input.is-checked .el-checkbox__inner::after {
  content: '';
  display: inline-block;
  width: 8px;
  height: 8px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
  position: absolute;
  top: 30%;
  left: 50%;
  transform: translate(-50%, -50%) rotate(45deg);
}

::ng-deep .el-checkbox.is-checked .el-checkbox__label {
  color: #3a3a3a;
  font-size: 1rem;
  font-family: var(--Font-Family-Book, 'Inter Variable');
}

::ng-deep .el-checkbox__input.is-checked + .el-checkbox__label {
  color: #3a3a3a;
}
::ng-deep .el-checkbox__inner {
  border-radius: 4px;
  width: 20px;
  height: 20px;
}
::ng-deep .el-checkbox__label {
  font-size: 1rem;
  font-family: var(--Font-Family-Book, 'Inter Variable');
  color: #616161;
}

button {
  border: none;
  padding: 12px;
  border-radius: 8px;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  font-size: 14px;
  min-width: 96px;
  height: 42px;
  text-transform: uppercase;
  position: relative;
  z-index: 0;
  overflow: hidden;
  font-family: 'GlobotipoTexto-Bold';

  &.primary {
    background: linear-gradient(90deg, #0079fd, #2d51fb, #5a29fa, #8800f8);
    color: #fff;
  }

  &.secondary {
    position: relative;
    display: inline-block;
    background-color: transparent;
    color: transparent;
    background-image: linear-gradient(#0079fd, #2d51fb, #5a29fa, #8800f8);
    background-clip: text;
    text-transform: uppercase;
    font-weight: 800;
    font-size: 14px;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      border-radius: inherit;
      padding: 1px;
      background: linear-gradient(90deg, #0079fd, #2d51fb, #5a29fa, #8800f8);
      mask:
        linear-gradient(#fff 0 0) content-box,
        linear-gradient(#fff 0 0);
      mask-composite: exclude;
    }
  }

  .label {
    line-height: 1;
    text-align: center;
    display: inline-block;
    flex-grow: 1;
  }
}

@media (max-width: 768px) {
  img {
    height: 146px;
  }
}

::ng-deep .el-checkbox__inner {
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
}

::ng-deep .el-checkbox__inner::after {
  margin: 0 !important;
  top: 36% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) rotate(45deg) !important;
}
