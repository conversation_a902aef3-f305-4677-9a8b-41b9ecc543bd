import {
  Component,
  EventEmitter,
  Input,
  OnD<PERSON>roy,
  OnInit,
  Output,
  SimpleChang<PERSON>,
} from '@angular/core'
import { InfoStateService } from './info-state.service'
import {
  CreativeEventType,
  CreativeHistory,
  CreativeItem,
  CreativeUsageHistory,
} from '@app/core/services/no-auth/digital/model/digital-interface-utils'
import { BehaviorSubject, Observable, Subscription, of } from 'rxjs'
import { BytesPipe } from '@app/lib/bytes.pipe'
import { format } from 'date-fns'
import { MatDialogConfig } from '@angular/material/dialog'
import { TimelineData, formatTypes } from '../../utils/dictionary'
import { Router } from '@angular/router'
import { ImageService } from '../../services/image.service'
import { DigitalService } from '@app/core/services/no-auth/digital/digital.service'
import { catchError, finalize, tap } from 'rxjs/operators'
import { environment } from '@env/environment'
import { CreativeStatus } from '@app/features/digital/simulacao-digital/digital-creative/pages/creative-details-page/model/creative-status.enum'
import { DetailGTMSlide } from '@app/shared/components/education-side-menu/model/education-gtm'
import { AssetDrawerService } from '../../services/asset-drawer.service'

@Component({
  selector: 'app-creative-info',
  templateUrl: './creative-info.component.html',
})
export class CreativeInfoComponent implements OnInit, OnDestroy {
  @Input() drawerOpen = false
  @Input() isGallery = true
  @Input() replaceCreative = false
  @Output() deleteCreative = new EventEmitter<CreativeItem>()
  @Output() onReplaceCreative = new EventEmitter<CreativeItem>()

  creativeInfo = new CreativeItem()
  subs = new Subscription()
  bytesPipe = new BytesPipe()
  dialogConfig = new MatDialogConfig()
  showPlayButton: boolean = true
  loading$ = new BehaviorSubject<boolean>(true)
  errorMessage: string
  isVisible = true
  currentImageSelected = 0
  currentTab = 0
  creativeInfoGtm: DetailGTMSlide = {
    dataArea: 'meus_anuncios',
    dataSection: 'criativos',
    dataLabel: 'informacoes_do_criativo',
  }

  history$: Observable<CreativeHistory[]> = of([])
  usageHistory$: Observable<CreativeUsageHistory[]> = of([])

  processedStatusHistory = [new TimelineData()]
  processedUsageHistory = [new TimelineData()]

  creativeThumbnailURL = `${environment.api.vaultBaseApi}/images/globo-no-thumb.png`
  creativeZipDetail
  isLoading: boolean = false
  thumbnailUrl: string | null = null
  creativeType: 'video' | 'image' | 'unknown' = 'unknown'
  error: string

  constructor(
    private assetDrawerService: AssetDrawerService,
    private infoStateService: InfoStateService,
    private imageService: ImageService,
    private digitalService: DigitalService,
    private router: Router,
  ) {}

  // Update the click handler in the component
  updateThumbnailURL(index: number) {
    if (this.creativeZipDetail && this.creativeZipDetail.content[index]) {
      this.creativeThumbnailURL = this.creativeZipDetail.content[index].s3Url // Assuming s3Url is the property for the image URL
    }
  }

  fetchZipDetail() {
    this.isLoading = true
    this.digitalService.getZipDetail(this.creativeInfo.requestId).subscribe(
      res => {
        this.creativeThumbnailURL = res.content[0].s3Url
        this.creativeZipDetail = res
        this.isLoading = false
      },
      error => {
        console.error('Erro ao buscar detalhes do ZIP', error)
        this.isLoading = false
      },
    )
  }

  initializeCreativeInfo() {
    if (this.creativeInfo.extension.endsWith('zip')) {
      if (this.creativeInfo.zipDetail) {
        this.creativeZipDetail = this.creativeInfo.zipDetail
        this.creativeThumbnailURL =
          this.creativeZipDetail.content[0]?.s3Url || this.creativeThumbnailURL // Define a URL da primeira imagem do zip
        this.setCreativeType(this.creativeZipDetail.content[0]?.extension) // Verifica o tipo do criativo
      } else {
        this.fetchZipDetail()
      }
    } else {
      this.creativeThumbnailURL = this.creativeInfo.s3Url
      this.setCreativeType(this.creativeInfo.extension) // Verifica o tipo do criativo
    }
  }

  // Método para definir o tipo do criativo
  setCreativeType(extension: string) {
    const imageExtensions = ['heic', 'jpg', 'jpeg', 'png', 'gif']
    const videoExtensions = ['mp4', 'mov', 'avi']

    const ext = extension.toLowerCase()

    if (imageExtensions.some(type => ext.endsWith(type))) {
      this.creativeType = 'image'
    } else if (videoExtensions.some(type => ext.endsWith(type))) {
      this.creativeType = 'video'
    } else {
      this.creativeType = 'unknown' // Caso não seja reconhecido
    }
  }

  ngOnInit() {
    this.subs.add(
      this.infoStateService.getCreativeInfo$.subscribe(r => {
        this.creativeInfo = r
        this.initializeCreativeInfo() // Chama o método de inicialização
        this.loadHistory()
      }),
    )
  }

  get formatTypes() {
    return formatTypes
  }

  handleStatusTimelineData(creativeList: CreativeHistory[]) {
    return creativeList.map(creative => ({
      title: creative.type.title,
      event: creative.type.eventName,
      eventTime: creative.eventTime,
      description: creative.type.description,
      obs: creative.observation,
    }))
  }

  handleUsageTimelineData(creativeList: CreativeUsageHistory[]) {
    return creativeList.map(creative => ({
      title: creative.titulo,
      event: '',
      eventTime: creative.dataCriacao,
      obs: '',
    }))
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.drawerOpen) {
      if (!changes.drawerOpen.currentValue) {
        this.resetState() // Redefine o estado quando o drawer é fechado
      }
    }
  }

  loadHistory() {
    const selectedEndpoint = this.isGallery
      ? this.digitalService.getCreativeStatusHistory(
          this.creativeInfo.id.toString(),
        )
      : this.digitalService.getCreativeStatusHistoryFromExternal(
          this.creativeInfo.id.toString(),
        )

    this.history$ = selectedEndpoint.pipe(
      tap(response => {
        this.processedStatusHistory = this.handleStatusTimelineData(
          response.filter(
            creative =>
              creative.type.eventName !== CreativeEventType.ASSOCIATED &&
              creative.type.eventName !== CreativeEventType.ERROR,
          ),
        ).reverse()
        this.loading$.next(true)
      }),
      catchError(error => {
        this.errorMessage = `Error: ${error.status} - ${environment.showExplicitError ? error.message : 'Ocorreu um erro de comunicação com o servidor'}`
        this.loading$.next(false)
        return of(null)
      }),
      finalize(() => this.loading$.next(false)),
    )

    this.usageHistory$ = this.digitalService
      .getCreativeUsage(this.creativeInfo.externalId)
      .pipe(
        tap(r => {
          this.processedUsageHistory = this.handleUsageTimelineData(r).reverse()
          this.loading$.next(true)
        }),
        catchError(error => {
          this.errorMessage = `Error: ${error.status} - ${environment.showExplicitError ? error.message : 'Ocorreu um erro de comunicação com o servidor'}`
          this.loading$.next(false)
          return of(null)
        }),
        finalize(() => this.loading$.next(false)),
      )
  }
  resetState() {
    this.creativeZipDetail = null
    this.creativeThumbnailURL = `${environment.api.vaultBaseApi}/images/globo-no-thumb.png`
    this.currentImageSelected = 0
  }

  ngOnDestroy(): void {
    this.subs.unsubscribe()
  }

  close() {
    this.assetDrawerService.closeAssetModal()
  }

  transformDimension(dimension: string) {
    return dimension ? dimension.replace(',', 'x') : dimension
  }

  playVideo(videoElement: HTMLVideoElement) {
    videoElement.play()
    this.showPlayButton = false
  }

  onVideoPause() {
    this.showPlayButton = true
  }

  onVideoPlay() {
    this.showPlayButton = false
  }

  transformSize(size: number) {
    return size ? this.bytesPipe.transform(size) : size
  }

  formattedDate(dateStr: string) {
    return dateStr ? format(new Date(dateStr), "dd/MM 'às' HH'h'mm") : dateStr
  }

  handleDelete() {
    if (this.isGallery) {
      this.close()
      this.imageService.handleDelete(
        this.creativeInfo.name,
        [this.creativeInfo.requestId],
        this.isGallery,
      )
    } else {
      this.close()
      this.deleteCreativeOutGallery()
    }
  }

  viewPreview(creativeItem) {
    const creativeFormatKey = this.getEnumKeyByValue(
      formatTypes,
      creativeItem.format,
    )
    this.router.navigate(['digital/criativo/pre-visualizacao'], {
      queryParams: {
        creativeFormat: creativeFormatKey,
        idCreative: creativeItem.id,
        galleryCreative: true,
        showPreview: true,
      },
    })
  }

  getEnumKeyByValue(enumObj, enumValue) {
    return Object.keys(enumObj).find(key => enumObj[key] === enumValue)
  }

  getStringAfterDot(input: string): string {
    const dotIndex = input.indexOf('.')

    if (dotIndex !== -1) {
      return input.substring(dotIndex + 1)
    } else {
      return input
    }
  }

  setLoadingWhenTabChange(event: number) {
    this.loading$.next(true)
    this.currentTab = event
  }

  onToggleElement() {
    this.isVisible = false // Remove o elemento da visualização
    this.loading$.next(true)
    // Adiciona o elemento de volta após 3 segundos
    setTimeout(() => {
      this.isVisible = true
    }, 0)
  }
  deleteCreativeOutGallery() {
    this.deleteCreative.emit(this.creativeInfo)
  }
  replaceCreativeAction() {
    this.onReplaceCreative.emit(this.creativeInfo)
  }
  checkDisabledDelete(status) {
    if (
      (status === CreativeStatus.CONTEUDO_PENDENTE ||
        status === CreativeStatus.CONTEUDO_APROVADO) &&
      this.isGallery === false
    ) {
      return true
    } else {
      return false
    }
  }
}
