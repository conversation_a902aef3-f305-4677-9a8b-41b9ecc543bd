import { Component, Inject } from '@angular/core'
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog'
import { DialogGenericData } from '../models/model'
import { AssetsItem } from '@app/core/services/no-auth/digital/model/digital-interface-utils'
import { AssetsGalleryService } from '@app/features/adstudio/services/assets-gallery.service'
import { GeradorCriativosService } from '@app/features/adstudio/services/gerador-criativos.service'
import { FooterActionService } from '@app/features/adstudio/services/footer-action.service'

@Component({
  selector: 'app-dialog-assets-list',
  templateUrl: './dialog-assets-list.component.html',
  styleUrls: ['./dialog-assets-list.component.scss'],
})
export class DialogAssetsListComponent {
  countSelected = 0
  assetSelected: AssetsItem

  get isFlow4Selection(): boolean {
    return this.data?.isFlow4Selection || this.data?.context === 'flow4' || false
  }

  constructor(
    public dialogRef: MatDialogRef<DialogAssetsListComponent>,
    @Inject(MAT_DIALOG_DATA) public data: DialogGenericData,
    private galleryService: AssetsGalleryService,
    private service: GeradorCriativosService,
    private footerAction: FooterActionService
  ) { }

  selectImage() {
    this.service.handleUsingGalleryImage(true)
    this.footerAction.showFooter()
    this.dialogRef.close(true)
  }

  selectAsset(assetsList: AssetsItem[]) {
    this.galleryService.updateGalleryAssets(assetsList)
    this.assetSelected = assetsList.find(img => img.selected)
    this.countSelected = assetsList.filter(img => img.selected).length
  }
}
