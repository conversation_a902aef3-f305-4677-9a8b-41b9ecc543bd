<div class="flex flex-col w-full gap-3 relative group" (mouseenter)="showActions = true"
  (mouseleave)="showActions = false" #target>
  <div
    class="w-full flex justify-center items-center overflow-hidden h-[149px] relative bg-overlay-luminosity-negative_1-light rounded-2xl"
    [ngClass]="{
      'opacity-50': opacityCard
    }">
    <div class="h-full w-full" [ngClass]="{
        'border-dashed border-2 border-link-hover-gradient-p66 rounded-2xl':
          selected
      }" [ngSwitch]="creative.format">
      <div class="flex items-center justify-center w-full h-full">
        <img class="h-full w-full object-contain" [src]="creativeThumbnail" alt="Thumbnail" />
      </div>
      <div class="flex items-center justify-center w-full h-full" *ngSwitchDefault>
        <img class="h-full w-full object-contain" [src]="
            creative.s3Url
              ? creative.s3Url
              : '../../../../../../assets/imgs/globo.svg'
          " alt="Fallback Image" />
      </div>
    </div>
    <div class="absolute inset-0 z-0 ">
    </div>


    <div class="absolute w-full flex transition-all duration-500 ease-in-out justify-between inset-0 p-2 z-10">
      <div *ngIf="showCheck" [ngClass]="{
          'lg:opacity-100': showActions || selected || isAssociateCreative,
          'lg:opacity-0 lg:cursor-none': !(
            showActions ||
            selected ||
            isAssociateCreative
          ),
          hidden: hiddenActions
        }" class="cursor-pointer h-fit z-20" data-element="checkbox"
        [attr.data-state]="!selected ? 'checked' : 'unchecked'" [attr.data-area]="
          !isChangeAssociateCreative
            ? 'associacao_criativos'
            : 'substituicao_criativos'
        " [attr.data-section]="
          !isChangeAssociateCreative
            ? 'selecao_criativos'
            : 'criativo_a_ser_substituido'
        " [attr.data-label]="'criativo_selecionado.' + positionItemCreative">
        <img (click)="sendCreativeSelectEvent()"
          class="w-[40px] h-[40px] rounded-full bg-gradient-to-b from-overlay-image-white-light to-overlay-image-white-dark"
          [src]="
            '../../../../../../assets/imgs/' +
            (selected
              ? isAssociateCreative
                ? 'checkbox-gradient-check'
                : 'checkbox-area-check'
              : 'checkbox-area') +
            '.png'
          " data-element="checkbox" [attr.data-state]="!selected ? 'checked' : 'unchecked'" [attr.data-area]="
            !isChangeAssociateCreative
              ? 'associacao_criativos'
              : 'substituicao_criativos'
          " [attr.data-section]="
            !isChangeAssociateCreative
              ? 'selecao_criativos'
              : 'criativo_a_ser_substituido'
          " [attr.data-label]="'criativo_selecionado.' + positionItemCreative" />
      </div>
      <div *ngIf="disclaimerMessage" [attr.class]="
          'absolute bottom-4 flex text-30 leading-23 tracking-20 font-regular font-book items-center justify-center left-3 border h-9 z-40 p-2 w-fit rounded-xl' +
          ' ' +
          disclaimerClass
        ">
        {{ disclaimerMessage }}
      </div>
      <div class="cursor-pointer h-fit" appPopUpMenu [menuItems]="menuItems" menuSide="right" [ngClass]="{
          'lg:opacity-100': showActions,
          'lg:opacity-0 lg:cursor-none': !showActions,
          hidden: isAssociateCreative || hiddenActions,
          'absolute right-0': !showCheck
        }">
        <div
          class="flex justify-center items-center w-[40px] h-[40px] rounded-full bg-gradient-to-b from-white to-gray-300 shadow-xl">
          <mat-icon class="!w-auto !h-auto text-[30px] text-neutral-600 material-symbols-outlined">more_vert</mat-icon>
        </div>
      </div>
    </div>
  </div>

  <!-- Informações abaixo do card -->
  <div class="flex flex-col gap-1" [ngClass]="{ 'opacity-50': opacityCard }">
    <div class="w-full truncate ...">
      <adtech-typography variant="body" size="small" as="div">{{
        creative.name
        }}</adtech-typography>
    </div>
    <div>
      <adtech-typography variant="body" size="small" styles="!text-content-secondary-light text-sm">
        {{ creative.format }}
      </adtech-typography>
    </div>
  </div>
</div>
