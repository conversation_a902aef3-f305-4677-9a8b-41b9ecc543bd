<div class="container-c-dialog flex flex-column gtm-element-visibility">
  <div
    class="c-dialog-display-format__header c-dialog-termos d-flex justify-content-center align-items-center c-dialog-header"
  >
    <div class="image"></div>
  </div>
  <div class="c-dialog-display-format__content c-dialog-termos c-dialog-body">
    <h4>Bem-vindo ao Gerador de Criativos da Globo.</h4>
    <div>
      <p>
        Conheça nossa nova ferramenta, desenvolvida para facilitar a criação de
        criativos digitais em diversos formatos, com o poder da inteligência
        artificial. Explore o Gerador de Criativos e potencialize os resultados
        das suas campanhas!
      </p>
    </div>
  </div>
  <div class="d-flex flex-column c-dialog-termos c-dialog-footer">
    <div
      class="flex check-container"
      data-element="checkbox"
      data-state="activated"
      data-area="gerador_criativos"
      data-section="tela_termos_de_uso"
      data-label="Li_e_estou_de_acordo_com_o_Termo_de_Uso"
    >
      <el-checkbox [(ngModel)]="estouDeAcordo">
        Li e estou de acordo com os
        <a class="text-[#616161]" target="_blank" href="https://termos.globoads.globo/"
          ><u> <b class="text-[#616161]">Termos de Uso.</b></u></a
        >
      </el-checkbox>
    </div>
    <div class="footer-controls d-flex gap-4 w-100 d-flex justify-content-end">
      <button
        (click)="aceitar(false)"
        class="secondary"
        data-element="button"
        data-state="activated"
        data-area="gerador_criativos"
        data-section="tela_termos_de_uso.recusar._final_etapa"
        data-label="recusar"
        data-keyword="recusar"
      >
        RECUSAR
      </button>
      <button
        (click)="aceitar(true)"
        [disabled]="!estouDeAcordo"
        class="primary"
        data-element="button"
        data-state="activated"
        data-area="gerador_criativos"
        data-section="tela_termos_de_uso.aceitar._final_etapa"
        data-label="aceitar"
        data-keyword="aceitar"
      >
        ACEITAR
      </button>
    </div>
  </div>
</div>
