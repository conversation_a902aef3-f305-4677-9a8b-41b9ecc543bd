import { NgModule } from '@angular/core'
import { CommonModule } from '@angular/common'
import { PreviewCreativeComponent } from './preview-creative.component'
import { RouterModule, Routes } from '@angular/router'
import { SharedModule } from '@app/shared/shared.module'
import { MatLegacyTabsModule as MatTabsModule } from '@angular/material/legacy-tabs'
import { CreativeComponentsModule } from '../components/creative-components.module'
import { PreviewImageCarrosselModule } from './preview-image-carrossel/preview-image-carrossel.module'
import { PreviewImagePublicityModule } from './preview-image-publicity/preview-image-publicity.modules'
import { ButtonPreviewBrandGloboPagesModule } from './scoped-components/button-preview-brand-globo-pages/button-preview-brand-globo-pages.module'
import { FramePreviewModule } from './scoped-components/frame-preview/frame-preview.module'
const routes: Routes = [
  {
    path: '',
    component: PreviewCreativeComponent,
  },
]
@NgModule({
  declarations: [PreviewCreativeComponent],
  imports: [
    RouterModule.forChild(routes),
    CommonModule,
    SharedModule,
    MatTabsModule,
    CreativeComponentsModule,
    ButtonPreviewBrandGloboPagesModule,
    FramePreviewModule,
    PreviewImagePublicityModule,
    PreviewImageCarrosselModule,
  ],
  exports: [PreviewCreativeComponent],
})
export class PreviewCreativeModule {}
