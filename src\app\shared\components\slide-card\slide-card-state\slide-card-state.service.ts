import { Injectable } from '@angular/core'
import { BehaviorSubject, Observable } from 'rxjs'
import { map } from 'rxjs/operators'

export type ModalType = 'creative' | 'asset' | null

@Injectable({
  providedIn: 'root',
})
export class SlideCardStateService {
  private cardToggleSubject = new BehaviorSubject<boolean>(false)
  private modalStateSubject = new BehaviorSubject<{
    open: boolean
    type: ModalType
  }>({ open: false, type: null })

  setCardToggleSubject(toggle: boolean) {
    this.cardToggleSubject.next(toggle)
  }

  get cardToggleSubject$() {
    return this.cardToggleSubject.asObservable()
  }

  setCardSingleToggleSubject(toggle: boolean) {
    const currentState = this.modalStateSubject.value
    this.modalStateSubject.next({ ...currentState, open: toggle })

    this.cardToggleSubject.next(toggle)
  }

  get cardSingleToggleSubject$() {
    return this.modalStateSubject.asObservable().pipe(map(state => state.open))
  }

  openModal(type: ModalType) {
    this.modalStateSubject.next({ open: true, type })
  }

  closeModal() {
    this.modalStateSubject.next({ open: false, type: null })
  }

  get modalState$(): Observable<{ open: boolean; type: ModalType }> {
    return this.modalStateSubject.asObservable()
  }

  get isAssetModalOpen$(): Observable<boolean> {
    return this.modalStateSubject
      .asObservable()
      .pipe(map(state => state.open && state.type === 'asset'))
  }

  get isCreativeModalOpen$(): Observable<boolean> {
    return this.modalStateSubject
      .asObservable()
      .pipe(map(state => state.open && state.type === 'creative'))
  }
}
