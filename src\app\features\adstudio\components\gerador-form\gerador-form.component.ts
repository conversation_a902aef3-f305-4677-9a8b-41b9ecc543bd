import {
  AssetRequest,
  AssetsItem,
} from '@app/core/services/no-auth/digital/model/digital-interface-utils'
import { Observable, Subscription } from 'rxjs'
import { CustomFormatModel } from './../../models/gerador-formatos.model'
import { Component, OnInit } from '@angular/core'
import { GeradorFormModel } from '../../models/gerador-form.model'
import { GeradorCriativosService } from '../../services/gerador-criativos.service'
import {
  AbstractControl,
  FormControl,
  FormGroup,
  Validators,
} from '@angular/forms'
import { GeradorInputModel } from '../../models/gerador-input-mode'
import { GeradorColorsModel } from '../../models/gerador-colors.model'
import {
  GeradorImageModel,
  GeradorImageRequest,
  GeradorImageResponse,
} from '../../models/gerador-image.model'
import { GeradorDisclaimerModel } from '../../models/gerador-disclaimer.model'
import { StepsDataService } from '../../services/steps-data.service'
import { GeradorCriativosHubService } from '../../services/gerador-criativos-hub.service'
import { AnaliticsMetaData } from '../../models/analitcs-metadata.model'
import { ErrorModel } from '../../models/errors-model'
import { isMobile } from '@app/shared/utils/mobile.helper'
import { GeradorDimensionsImages } from '../../models/gerador-dimensions.model'
import GeradorIAImage from '../../models/gerador-ia-image.model'
import { DetailLayoutImagesGerador } from '../../models/images-layout-model'
import { FooterActionService } from '../../services/footer-action.service'
import { MatDialog, MatDialogConfig } from '@angular/material/dialog'
import { Router } from '@angular/router'
import { ErrosGeradorService } from '../../services/erros-gerador.service'
import { noOnlySpacesValidator } from '../../utils/validator-only-spaces'
import { LayoutInfo } from '../../models/layout-info.model'
import {
  ImageMethodOption,
  ImageSelectionMethod,
} from '../../models/image-select-method.model'
import { DialogAssetsListComponent } from '@app/features/digital/creative-manager/dialogs/assets/dialog-assets-list/dialog-assets-list.component'
import { AssetsGalleryService } from '../../services/assets-gallery.service'
import { FooterAction } from '../../utils/footer.enum'
import { IGenerateCreativeRequest } from '../../models/criativo-request.model'
import { SendCreativeService } from '../../services/send-creative.service'
import { NotificationService } from '@app/core/services/notification/notification.service'
import { take } from 'rxjs/operators'

@Component({
  selector: 'app-gerador-form',
  templateUrl: './gerador-form.component.html',
  styleUrls: ['./gerador-form.component.scss'],
})

export class GeradorFormComponent implements OnInit {
  currentStep$: Observable<GeradorFormModel>
  step_1: CustomFormatModel[]
  geradorForm: FormGroup

  format: string
  step_2: GeradorInputModel[]
  step_3: GeradorInputModel[]
  colorsFormAttrs: GeradorColorsModel[]
  imageType: GeradorImageModel[]
  backgroundType: GeradorImageModel[]
  step_4: GeradorInputModel[]
  objTypeForm: GeradorInputModel[]
  messageError: string
  imageColors: GeradorColorsModel[]
  step_5: GeradorIAImage[]
  step_6: GeradorIAImage[]
  disableGeraMais: boolean = false
  disclaimers: GeradorDisclaimerModel[]
  currentStep: number
  typeMsg: number
  STEP_FORM: string
  error: string | null = null
  loading: boolean = true
  gerandoImagem: boolean = false
  limiteAtingidoMetaDados: AnaliticsMetaData[]
  lastStep: number
  erroAoGerarTexto: boolean = false
  erroAoGerarImage: boolean = false

  savelAllImages: boolean = false
  dataImageDimension: GeradorDimensionsImages
  detailsImageLayout: DetailLayoutImagesGerador = {
    colorBackground: '#FFD2D0',
    colorDetail: '#FF716B',
    colorText: '#3F0F0D',
    textAds: '',
    textButton: '',
    width: 0,
    height: 0,
    sourceLogo: '',
    sourceImage: '',
    colorBackgroundLogo: '',
  }
  selectedLayoutId

  private isCapturing: boolean = false
  layoutSelected = 0
  selectedOptionMethodImage: string | null = null

  originAdsStudio: boolean = false

  statusCriativo: boolean

  stepOneData = { layout: 'Billboard' }
  stepTwoData = { logo: '', coloBackgroundLogo: 'white' }
  stepThreeData = {
    mainText: '',
    ctaText: '',
    layoutFirstColor: '',
    layoutSecondColor: '',
    layoutThirdColor: '',
  }
  stepFiveData = {
    imageSelected: {
      url: '',
    },
  }
  stepSixData = {
    layoutSelected: null,
  }
  layoutInfo: LayoutInfo = {
    type: '',
    width: 0,
    height: 0,
    aspectRadio: '',
  }
  subscription = new Subscription()
  cameFromAssets: boolean
  halfpageWrap: boolean = false
  footerSubs: Subscription
  images: any[] = []
  changeCreative = false
  indexDisclaimer: number
  generateMore: boolean // controla a geração de imagem para quando a opção de gerar mais for acionada
  maxImages: number = 12
  messageErro: string = `Detectamos uma instabilidade ao gerar as imagens, por favor tente novamente em alguns instantes ou entre em contato com o nosso suporte no telefone
      <strong class="text-content-primary-light">4004-8011</strong>.`
  isMobile: boolean = false
  dialogConfig = new MatDialogConfig()
  geradorStartFormData = {
    step_1: new FormGroup({
      layout: new FormControl('', [Validators.required]),
    }),
    step_2: new FormGroup({
      name: new FormControl('', [Validators.required, noOnlySpacesValidator()]),
      type: new FormControl('', [Validators.required, noOnlySpacesValidator()]),
      objective: new FormControl('', [
        Validators.required,
        noOnlySpacesValidator(),
      ]),
      logo: new FormControl('', [Validators.required]),
      detail: new FormControl(''),
      saveInfo: new FormControl(false),
      colorLogo: new FormControl(''),
    }),
    step_3: new FormGroup({
      mainText: new FormGroup({
        currentIndex: new FormControl(0),
        texts: new FormControl([], [Validators.required]),
      }),
      ctaText: new FormGroup({
        currentIndex: new FormControl(0),
        texts: new FormControl([], [Validators.required]),
      }),
      layoutFirstColor: new FormControl('#FFD2D0'),
      layoutSecondColor: new FormControl('#3F0F0D'),
      layoutThirdColor: new FormControl('#FF716B'),
    }),
    step_4: new FormGroup({
      imageType: new FormControl(1),
      imageBackgroundType: new FormControl(1),
      peopleNumber: new FormControl('1 Pessoa'),
      imageDescription: new FormControl(''),

      desc: new FormControl(''),

      imageFirstColor: new FormControl(null),
      imageSecondColor: new FormControl(null),
      imageThirdColor: new FormControl(null),

      imageFirstColorName: new FormControl('MistyRose'),
      imageSecondColorName: new FormControl('Maroon'),
      imageThirdColorName: new FormControl('Salmon'),
    }),
    step_5: new FormGroup({
      imageSelected: new FormControl('', [Validators.required]),
    }),
    step_6: new FormGroup({
      layoutSelected: new FormControl('', [Validators.required]),
    }),
    results: new FormGroup({
      imageByIa: new FormControl(''),
    }),
    saveAllImages: new FormControl(false),
  }

  public imageMethodOptions: ImageMethodOption[] = [
    {
      value: ImageSelectionMethod.MY_IMAGES,
      label: 'Usar minhas imagens',
      icon: 'photo_library.svg',
      description: 'Selecione uma imagem da sua galeria ou faça upload',
    },
    {
      value: ImageSelectionMethod.CREATE_WITH_AI,
      label: 'Criar com IA',
      icon: 'magic-edit-white.svg',
      description: 'Gere imagens personalizadas usando inteligência artificial',
    },
  ]
  constructor(
    private readonly service: GeradorCriativosService,
    private readonly hubService: GeradorCriativosHubService,
    private readonly stepsDataService: StepsDataService,
    private readonly footerAction: FooterActionService,
    private readonly creativeService: SendCreativeService,
    private readonly dialog: MatDialog,
    private readonly router: Router,
    private readonly errorService: ErrosGeradorService,
    private readonly assetGalleryService: AssetsGalleryService,
    private readonly notificationService: NotificationService,
  ) {
    this.savelAllImages = false
    this.geradorForm = new FormGroup({ ...this.geradorStartFormData })
    this.cameFromAssets = false
  }

  ngOnInit(): void {
    this.subscription.add(
      this.service.getCurrentStep().subscribe(el => {
        this.currentStep = el.step
        this.STEP_FORM = 'step_' + this.currentStep
      }),
    )

    this.currentStep$ = this.service.getCurrentStep()

    this.subscription.add(
      this.service.getLastStep().subscribe(last => (this.lastStep = last)),
    )

    this.loadData()
    this.loadBusinessName()

    this.subscription.add(
      this.service.getNotification().subscribe((step: number) => {
        this.checkCurrentStep()
        if (step === 5 && this.lastStep !== 6) {
          this.callGenerateImages()
        }
      }),
    )

    this.subscription.add(
      this.service.listenClearRequest().subscribe((clear: boolean) => {
        if (clear) {
          this.stepsDataService.clearStepsData()
          this.service.setCurrentStep(0)
          this.geradorForm.setValue({ ...this.geradorStartFormData })
          this.loadData()
          this.loadBusinessName()
        }
      }),
    )

    this.subscription.add(
      this.errorService.error$.subscribe(error => {
        if (error) {
          this.erroAoGerarTexto = true
          if (error.status === 422) {
            switch (error.title) {
              case 'SAFETY':
                this.indexDisclaimer = 6
                break
              default:
                this.indexDisclaimer = 4
                break
            }
          }
          this.service.setCurrentStep(1)
        }
      }),
    )

    this.isMobile = isMobile()
    this.service.setStepFormValues(2, {})
    this.geradorForm
      .get(this.STEP_FORM)
      ?.updateValueAndValidity({ emitEvent: true })

    const subs = this.footerAction.event$.subscribe(el => {
      if (el.action === FooterAction.NEXT && el.step === 6) {
        this.callGenerateCriative()
        subs.unsubscribe()
      }
      if (el.action === FooterAction.NEXT && el.step === 4) {
        this.erroAoGerarImage = false
        this.callGenerateImages()
      }

      if (el.action === FooterAction.NEXT && el.step === 5) {
        this.uploadAllAssets()
      }

      if (el.action === FooterAction.NEXT && el.step === 3) {
        const assetSelected = this.service.getStepFormValues(5)?.imageSelected
        if (assetSelected) {
          this.cameFromAssets = true
          this.geradorForm.get('step_5')?.get('imageSelected').setValue({
            id: assetSelected.id,
            bucket_id: assetSelected.uuid,
            selected: true,
            url: assetSelected.src,
            type: 0,
          })
          this.geradorForm.updateValueAndValidity()
          this.saveAllStepsData()
          this.service.setCurrentStep(5)
        }
        this.footerAction.hideFooter()
      }

      if (el.action === FooterAction.BACK && el.step === 6) {
        this.goToBackStep()
      }
    })
  }



  uploadAllAssets(): void {
    if (this.geradorForm.get('saveAllImages')?.value) {
      const listAssetsToUpload: AssetRequest[] = []
      const payload = this.preparePayload()

      this.step_5.forEach((asset: GeradorIAImage, i: number) => {
        listAssetsToUpload.push({
          filtro: payload.scenarioType,
          name: this.generateImageName(i + 1),
          qtd_pessoas: payload.numberOfPeople,
          url: asset.url,
        })
      })

      this.assetGalleryService.uploadAsset(listAssetsToUpload).subscribe(
        _ => {
          this.notificationService.showToastSuccess(
            'Imagens salvas com sucesso!',
          )
        },
        error => {
          console.error(
            'Ocorreu um erro ao fazer o upload dos assets: ' + error,
          )
        },
      )
    }
  }
  generateImageName(ordemGerada: number): string {
    return 'Imagem_' + ordemGerada
  }

  callGenerateImages(): void {
    if (!this.images || this.images.length === 0 || this.generateMore) {
      if (this.gerandoImagem) {
        console.warn(
          'Geração de imagem já em andamento. Aguarde a finalização.',
        )
        return
      }

      this.gerandoImagem = true

      try {
        const payload = this.preparePayload()

        this.hubService.generateImageCampaign(payload).subscribe({
          next: (imagens: any[]) => {
            const novasImagens = imagens.slice(0, this.maxImages)
            this.images = novasImagens
            this.convertImages(novasImagens)
            this.gerandoImagem = false
            this.generateMore = false
          },
          error: err => {
            this.gerandoImagem = false
            this.generateMore = false
            if (err.status === 422) {
              this.erroAoGerarImage = true
              switch (err.title) {
                case 'SAFETY':
                  this.indexDisclaimer = 6
                  break
                default:
                  this.indexDisclaimer = 4
                  break
              }
              this.service.setCurrentStep(3)
            } else {
              this.tratarErrosApi(err)
            }
          },
        })
      } catch (error) {
        this.openErrorDialogAdstudio(this.navigateToHome.bind(this))
      }
    }
  }

  tratarErrosApi(err) {
    console.error('Erro ao gerar imagens:', err)
    if (err.origem === 'vertex') {
      this.error = 'Erro ao gerar imagens. Tente novamente mais tarde.'
      this.erroAoGerarImage = true
      this.service.setCurrentStep(3)
      this.gerandoImagem = false
      this.generateMore = true
    } else {
      this.openErrorDialogAdstudio(
        this.confirmedErrorImage.bind(this),
        this.confirmedErrorImage.bind(this),
        err,
      )
    }
  }

  convertImages(images: GeradorImageResponse[]) {
    images?.forEach((image: GeradorImageResponse, i) => {
      if (image) {
        this.step_5.push({
          url: image.url,
          id: i,
          bucket_id: image.id,
          selected: false,
          type: 0,
        })
      }
      this.gerandoImagem = false
      this.generateMore = false
    })
  }

  preparePayload(): GeradorImageRequest {
    return {
      businessName: this.step_2.find(el => el.control === 'name').value,
      businessArea: this.step_2.find(el => el.control === 'type').value,
      aspectRatio: this.getAspectRatio(),
      numberOfPeople: this.getNumberOfPeople(),
      peopleAction: this.getPeopleAction(),
      objectName: this.getObjectDescription(),
      colors: this.getColorsImage(),
      scenarioType: this.getScenarioType(),
    }
  }

  getAspectRatio(): string {
    return this.layoutInfo.aspectRadio ? this.layoutInfo.aspectRadio : '4:3'
  }

  getColorsImage(): string {
    let finalString = ''
    if (this.imageColors.length > 0) {
      this.imageColors.forEach(color => {
        finalString += color.nameColor + ' '
      })
    } else {
      this.colorsFormAttrs.forEach(color => {
        finalString += color.value + ' '
      })
    }

    return finalString
  }

  getScenarioType(): string {
    if (this.geradorForm.get('step_4').get('imageBackgroundType').value === 1) {
      return 'Complex'
    } else {
      return 'simple'
    }
  }

  getPeopleAction(): string {
    return this.geradorForm.get('step_4').get('imageDescription').value
  }

  getNumberOfPeople(): number {
    if (this.geradorForm.get('step_4').get('imageType').value === 2) {
      return 0
    } else {
      return parseInt(
        this.step_4.find(el => el.control === 'peopleNumber').value[0],
      )
    }
  }

  getObjectDescription(): string {
    return this.geradorForm.get('step_4').get('desc')?.value
  }

  updateFormByStepsData(): void {
    const masterControls = [
      ...this.step_1,
      ...this.step_2,
      ...this.step_4,
      ...this.step_5,
      ...this.step_6,
      ...this.colorsFormAttrs,
      ...this.imageColors,
      ...this.imageType,
      ...this.backgroundType,
      ...this.objTypeForm,
    ]
    let allValid = true
    masterControls.forEach((el: any) => {
      if (el.control) {
        const control = this.geradorForm.get(this.STEP_FORM)?.get(el.control)
        if (control) {
          const fakeControl = { value: el.value } as AbstractControl
          const validationResult = noOnlySpacesValidator()(fakeControl)
          if (!validationResult) {
            control.patchValue(el.value)
          } else {
            allValid = false
          }
        }
      }
    })

    this.geradorForm.updateValueAndValidity()
    if (allValid) {
      this.saveAllStepsData()
      if (this.currentStep === 1) {
        this.styleAppliedHalfpage()
      }
    }
    this.checkCurrentStep()
  }

  handleSelect(elementSelected: CustomFormatModel): void {
    if (!elementSelected.disabled) {
      this.step_1.forEach((el: CustomFormatModel) => {
        if (el.id === elementSelected.id && !elementSelected.disabled) {
          el.selected = true
        } else {
          el.selected = false
        }
      })
    }

    if (
      elementSelected.selector !== 'retangulo-medio' &&
      this.step_6.length < 3
    ) {
      this.step_6 = [
        {
          id: 0,
          selected: false,
          url: '../../assets/images/finish-1.svg',
          type: 1,
        },
        {
          id: 1,
          selected: false,
          url: '../../assets/images/finish-2.svg',
          type: 1,
        },
        {
          id: 2,
          selected: false,
          url: '../../assets/images/finish-2.svg',
          type: 1,
        },
      ]
    }
    if (
      elementSelected.selector === 'retangulo-medio' &&
      this.step_6.length === 3
    ) {
      this.step_6.pop()
    }

    this.layoutInfo = {
      type: elementSelected.title,
      width: elementSelected.larg,
      height: elementSelected.alt,
      aspectRadio: this.handleAspectRatioChanged(elementSelected.title),
    }

    if (!elementSelected.disabled) {
      this.changeValue()
    }
  }

  handleAspectRatioChanged(layoutType: string) {
    switch (layoutType) {
      case 'Retângulo médio':
        return '9:16'
      case 'Super Leaderboard':
        return '16:9'
      default:
        return '4:3'
    }
  }

  checkCurrentStep(): void {
    const formGroup = this.geradorForm.get(this.STEP_FORM) as FormGroup
    this.service.updateCurrentStep(formGroup?.valid)
  }

  loadData(): void {
    this.step_1 = [
      {
        title: 'Billboard',
        id: 1,
        larg: 970,
        alt: 250,
        selected: false,
        disabled: false,
        selector: 'billboard',
      },
      {
        title: 'Retângulo médio',
        id: 2,
        larg: 300,
        alt: 250,
        selected: false,
        disabled: false,
        selector: 'retangulo-medio',
      },
      {
        title: 'Maxiboard',
        id: 3,
        larg: 970,
        alt: 150,
        selected: false,
        disabled: false,
        selector: 'maxiboard',
      },
      {
        title: 'Super Leaderboard',
        id: 4,
        larg: 970,
        alt: 90,
        selected: false,
        disabled: false,
        selector: 'super-leaderboard',
      },
      {
        title: 'Half-page',
        id: 5,
        larg: 300,
        alt: 600,
        selected: false,
        disabled: false,
        selector: 'half-page',
      },
    ]

    this.step_2 = [
      {
        control: 'name',
        label: 'Nome do seu negócio',
        placeholder: 'Insira o nome do seu negócio',
        type: 'text',
        value: '',
      },
      {
        control: 'type',
        label: 'Tipo de negócio',
        placeholder: 'Lanchonete de comida saudável',
        type: 'text',
        value: '',
      },
      {
        control: 'objective',
        label: 'Objetivo do anúncio',
        placeholder: 'Selecione qual o objetivo do seu anúncio',
        type: 'select',
        options: [
          { title: 'Atrair novos clientes' },
          {
            title: 'Aumentar seguidores nas redes sociais',
            subOption: {
              title: 'Qual rede social?',
              value: '',
              label: 'Qual rede social?',
              placeholder: 'Instagram, Facebook, TikTok.',
            },
          },
          {
            title: 'Divulgar promoção ou campanha',
            subOption: {
              title: ' Que tipo de promoção?',
              label: ' Que tipo de promoção?',
              value: '',
              placeholder: 'Black Friday, Natal, Queima de estoque...',
            },
          },
          {
            title: 'Divulgar um cupom de desconto',
            subOption: {
              title: 'Qual o Cupom?',
              value: '',
              label: 'Qual o Cupom?',
              placeholder: 'GLOBO10, GLOBOFRIDAY, (ver exemplos).',
            },
          },
          {
            title: 'Lançar um produto ou serviço',
            subOption: {
              title: 'Qual produto/serviço?',
              label: 'Qual produto/serviço?',
              value: '',
              placeholder: 'Geladeira / Frete grátis/ Assistência técnica.',
            },
          },
          {
            title: 'Promover eventos',
            subOption: {
              title: 'Que tipo de evento?',
              label: 'Que tipo de evento?',
              value: '',
              placeholder: 'Inauguração, Feiras, Evento Beneficente.',
            },
          },
          {
            title: 'Outro',
            subOption: {
              title: 'Descreva o objetivo:',
              label: 'Descreva o objetivo:',
              value: '',
              placeholder: 'Descrição:',
            },
          },
        ],
        value: '',
      },
      {
        control: 'logo',
        label: 'Logotipo do seu negócio',
        placeholder: null,
        type: 'file',
        value: '',
        value2: 'white',
      },
    ]

    this.step_3 = [
      {
        control: 'mainText',
        label: 'Texto do Anúncio',
        placeholder: 'Lanchonete de comida saudável',
        type: 'text',
        value: '',
      },
      {
        control: 'ctaText',
        label: 'Texto do botão (CTA)',
        placeholder: 'Lanchonete de comida saudável',
        type: 'text',
        value: '',
      },
    ]

    this.step_4 = [
      {
        control: 'peopleNumber',
        label: 'Número de pessoas na imagem',
        placeholder: '1 pessoa',
        type: 'select',
        options: [{ title: '1 pessoa' }, { title: '2 pessoas' }],
        value: '1 pessoa',
      },
      {
        control: 'imageDescription',
        label: 'Descreva o que as pessoas estão fazendo',
        placeholder:
          'Uma mulher jovem, negra e sorridente, segurando um sanduíche saudável de pão integral com vegetais frescos e frango grelhado.',
        type: 'textarea',
        value: '',
      },
    ]

    this.step_5 = []

    this.step_6 = [
      {
        id: 0,
        selected: false,
        url: '../../assets/images/finish-1.svg',
        type: 1,
      },
      {
        id: 1,
        selected: false,
        url: '../../assets/images/finish-2.svg',
        type: 1,
      },
      {
        id: 2,
        selected: false,
        url: '../../assets/images/finish-2.svg',
        type: 1,
      },
    ]

    this.colorsFormAttrs = [
      {
        id: 1,
        value: '#ffd2d0',
        label: 'Fundo',
        control: 'layoutFirstColor',
        controlName: '',
      },
      {
        id: 2,
        value: '#3f0f0d',
        label: 'Fonte & Contorno do botão',
        control: 'layoutSecondColor',
        controlName: '',
      },
      {
        id: 3,
        value: '#ff716b',
        label: 'Detalhes',
        control: 'layoutThirdColor',
        controlName: '',
      },
    ]

    this.imageColors = []

    this.imageType = [
      {
        id: 1,
        label: 'Com pessoas',
        selected: true,
      },
      {
        id: 2,
        label: 'Com objetos',
        selected: false,
      },
    ]

    this.backgroundType = [
      {
        id: 3,
        label: 'Composto',
        selected: true,
      },
      {
        id: 4,
        label: 'Simples',
        selected: false,
      },
    ]

    this.objTypeForm = [
      {
        control: 'desc',
        label: 'Objeto em destaque',
        placeholder:
          'Sanduíche saudável com abacate, alface, tomate e frango grelhado em pão integral. Ao fundo, lanchonete moderna com plantas e decoração clean, transmitindo leveza.',
        type: 'textarea',
        value: '',
      },
    ]

    this.disclaimers = [
      {
        text: 'Recomendamos o uso de arquivos em formato <span class="span-disclaimer"> PNG com fundo transparente.</span>',
        type: 0,
      },
      {
        text: 'Esta é uma ferramenta em fase experimental; cenas com pessoas e alguns objetos podem ter aparência irreal.',
        type: 0,
      },
      {
        text: 'Você atingiu o limite de criação de imagens. Escolha uma das opções geradas para continuar.',
        type: 1,
      },
      {
        text: 'Selecione uma das opções geradas pela IA ou edite o texto conforme sua preferência.',
        type: 0,
      },
      {
        text: 'A descrição enviada contém elementos que violam  <span class="danger-disclaimer"> nossas diretrizes.</span> Por favor, revise e insira um texto adequado para continuar.',
        type: 2,
      },
      {
        text: `Você atingiu o limite de criação de imagens. Escolha uma das opções geradas e clique em 'Gerar Criativos' para continuar.`,
        type: 3,
      },
      {
        text: `Detectamos que a descrição enviada pode conter referências a atividades ilícitas ou inadequadas. Esse tipo de conteúdo não é permitido. Por favor, revise e insira um texto adequado para continuar. `,
        type: 2,
      },
    ]

    this.limiteAtingidoMetaDados = [
      {
        chave: 'class',
        valor: 'gtm-element-visibility',
      },
      {
        chave: 'data-element',
        valor: 'message',
      },
      {
        chave: 'data-state',
        valor: 'viewed',
      },
      {
        chave: 'data-area',
        valor: 'gerador_criativos',
      },
      {
        chave: 'data-section',
        valor: '5_finalizacao.escolha_uma_das_imagens',
      },
      {
        chave: 'data-label',
        valor: 'Menssagem erro, limite de geração de imagens atingido.',
      },
    ]
  }

  notifyError(error: ErrorModel) {
    this.erroAoGerarTexto = error.type === 1
    this.updateFormByStepsData()
  }

  loadBusinessName(): void {
    this.errorService.clearError()
    this.erroAoGerarImage = false
    this.erroAoGerarTexto = false
    const currentBusinessName = localStorage.getItem('companyName')
    const businessInput = this.step_2.find(el => el.control === 'name')
    businessInput.value = currentBusinessName
    this.updateFormByStepsData()
  }

  getCurrentStepForm(step: string): GeradorInputModel[] {
    switch (step) {
      case 'step_2':
        return this.step_2
      case 'step_3':
        return this.step_3
      case 'step_4':
        return this.step_4
      default:
        return [] as GeradorInputModel[]
    }
  }

  changeValue() {
    this.updateFormByStepsData()
  }

  changePalleteValue(color) {
    this.changeValue()
    if (color?.controlName) {
      this.geradorForm
        .get(this.STEP_FORM)
        ?.get(color.controlName)
        ?.patchValue(color.name)
    }
  }

  handleSaveInfo($event) {
    this.geradorForm.get(this.STEP_FORM)?.get('saveInfo')?.setValue($event)
  }

  handleSelectCard(card: GeradorImageModel) {
    card.selected = true
    if (card.id === 1 || card.id === 2) {
      this.imageType.find(el => el.id !== card.id).selected = false
      this.geradorForm.get(this.STEP_FORM)?.get('imageType').setValue(card.id)
    } else {
      this.backgroundType.find(el => el.id !== card.id).selected = false
      this.geradorForm
        .get(this.STEP_FORM)
        ?.get('imageBackgroundType')
        .setValue(card.id)
    }
    this.changeValue()
  }

  selectImage(image: GeradorIAImage, idTipoCard: number = 1): void {
    if (idTipoCard === 1) {
      this.step_5.map(el => {
        if (el.id !== image.id) {
          el.selected = false
        }
      })
      this.geradorForm.get(this.STEP_FORM)?.get('imageSelected').setValue(image)
    } else {
      this.step_6.map(el => {
        if (el.id !== image.id) {
          el.selected = false
        }
      })
      this.geradorForm.get('step_6').get('layoutSelected').setValue(image)
      this.selectedLayoutId = image.id
    }

    image.selected = true

    this.changeValue()
  }

  gerarMais(): void {
    this.generateMore = true
    this.callGenerateImages()
    this.checkMaxGerados()
  }

  checkMaxGerados(): boolean {
    if (this.step_5.length >= 12) {
      return true
    } else {
      return false
    }
  }

  saveAllStepsData(): void {
    const allStepsData = {}
    for (const step of Object.keys(this.geradorForm.controls)) {
      const stepGroup = this.geradorForm.get(step)?.value
      if (stepGroup) {
        allStepsData[step] = stepGroup
      }
    }
    this.stepsDataService.saveAllStepsData(allStepsData)
    this.service.setStepFormValues(this.currentStep, allStepsData)
  }

  goToBackStep(): void {
    this.service
      .getUsingGalleryImage()
      .pipe(take(1))
      .subscribe((usingGalley: boolean) => {
        if (usingGalley) {
          this.service.setCurrentStep(3, 6)
          this.service.handleUsingGalleryImage(false)
          this.footerAction.hideFooter()
        } else {
          this.service.setCurrentStep(this.currentStep - 2, 6)
        }
      })
  }

  returnDimensionsSkeleton() {
    if (this.isMobile) {
      return '160px'
    } else {
      return '235px'
    }
  }

  returnDimensionsSkeletonHeight() {
    if (this.layoutInfo.type === 'Retângulo médio') {
      return '364px'
    }
    if (this.isMobile) {
      return '112px'
    } else {
      return '164px'
    }
  }

  updateLayoutImage(imageLayoutDetail: DetailLayoutImagesGerador) {
    this.detailsImageLayout = imageLayoutDetail
  }

  private getCurrentText(textData): string {
    if (
      textData &&
      Array.isArray(textData.texts) &&
      typeof textData.currentIndex === 'number'
    ) {
      return textData.texts[textData.currentIndex] || 'Texto Padrão'
    }
    return 'Texto Padrão'
  }

  updateDetailsImageLayout() {
    this.subscription.add(
      this.stepsDataService.stepsData$.subscribe(data => {
        const step2Data = data['step_2'] || {}
        const step3Data = data['step_3'] || {}
        const step5Data = data['step_5'] || {}

        this.detailsImageLayout = {
          textAds: this.getCurrentText(step3Data?.mainText),
          textButton: this.getCurrentText(step3Data?.ctaText),
          colorText: step3Data?.layoutSecondColor,
          colorBackground: step3Data?.layoutFirstColor,
          colorDetail: step3Data?.layoutThirdColor,
          width: this.checkWidthImage(),
          height: this.chekHeightImage(),
          sourceImage: step5Data?.imageSelected?.url,
          sourceLogo: step2Data?.logo,
          colorBackgroundLogo: step2Data?.colorLogo,
        }
      }),
    )
  }

  prepareCriativoPayload(layoutSelecionado): IGenerateCreativeRequest {
    this.updateDetailsImageLayout()

    this.stepFiveData.imageSelected.url
    return {
      colorBackground: this.detailsImageLayout.colorBackground,
      sourceLogo: this.detailsImageLayout.sourceLogo,
      colorBackgroundLogo: this.detailsImageLayout.colorBackgroundLogo,
      colorDetail: this.detailsImageLayout.colorDetail,
      colorText: this.detailsImageLayout.colorText,
      textAds: this.detailsImageLayout.textAds,
      textButton: this.detailsImageLayout.textButton,
      format: layoutSelecionado,
      layout: this.selectedLayoutId + 1,
      sourceImage: (
        this.geradorForm.get('step_5')?.get('imageSelected')?.value as {
          url: string
        }
      ).url,
    }
  }

  async callGenerateCriative() {
    const layoutSelecionado: string = this.step_1.find(
      el => el.selected,
    ).selector
    const payload: IGenerateCreativeRequest =
      this.prepareCriativoPayload(layoutSelecionado)
    const subscription = this.hubService.generateCreative(payload).subscribe({
      next: async (response: GeradorImageResponse[]) => {
        const criativoGerado = response[0]
        const urlCriativo = criativoGerado.url
        const nameCriativo = `${layoutSelecionado}_ia_${Math.random().toString(36).slice(2, 11)}.png`
        const criativoFinal = await fetch(urlCriativo)
        const blobCriativo = await criativoFinal.blob()
        const file = new File([blobCriativo], nameCriativo, {
          type: 'image/png',
        })

        this.creativeService.updateFile(file)
        // this.sendFileS3AndValidate(file)
      },
      error: err => {
        console.error('Ocorreu um erro: ' + err)
      },
      complete: () => {
        subscription.unsubscribe()
      },
    })
  }

  openErrorDialogAdstudio(
    onConfirmCallback: () => void,
    onCancelCallback?: () => void,
    data?,
  ) {
    if (data.error) {
      data = data.error
    }

    if (data.status === 500) {
      this.dialogConfig.data = {
        message:
          'Estamos passando por um problema técnico. Por favor tente novamente mais tarde.',
        buttonConfirmed: 'Voltar',
        headerTitle: 'Erro interno',
        onConfirm: onConfirmCallback,
        onCancel: onCancelCallback,
      }
    } else {
      const errorOrigemText =
        data.origem === 'vertex' ? 'Erro na Vertex Api' : 'Erro no servidor'
      const erroMessage =
        data.detail ??
        'Ocorreu um erro desconhecido. Por favor, Tente novamente mais tarde.'

      this.dialogConfig.data = {
        message: erroMessage,
        buttonConfirmed: 'Voltar',
        headerTitle: errorOrigemText,
        onConfirm: onConfirmCallback,
        onCancel: onCancelCallback,
      }
    }

    this.dialogConfig.panelClass = ['delete-dialog', 'gtm-element-visibility']
  }

  navigateToHome() {
    this.router.navigate(['/home'])
  }

  confirmedErrorImage() {
    window.location.reload()
  }

  resetForm(): void {
    if (this.geradorForm) {
      this.geradorForm.reset()
    }
  }
  checkWidthImage() {
    const selectedItem = this.step_1.find(item => item.selected)
    const selectedTitle = selectedItem ? selectedItem.title : null
    switch (selectedTitle) {
      case 'Billboard':
        return 970
      case 'Retângulo Medio':
        return 300
      case 'Half-page':
        return 300
      default:
        return 970
    }
  }

  chekHeightImage() {
    switch (this.stepOneData?.layout) {
      case 'Billboard':
        return 250
      case 'Retângulo Medio':
        return 300
      case 'Half-Page':
        return 600
      default:
        return 250
    }
  }

  styleAppliedHalfpage() {
    const selectedItem = this.step_1.find(item => item.selected)
    const selectedTitle = selectedItem ? selectedItem.title : null
    switch (selectedTitle) {
      case 'Half-page':
        this.halfpageWrap = true
        break
      default:
        this.halfpageWrap = false
        break
    }
  }
  onOptionClickImageSelection(option: string) {
    this.selectedOptionMethodImage = option

    if (option === 'minhasImagens') {
      this.footerAction.hideFooter()
      const dialogRef = this.dialog.open(DialogAssetsListComponent, {
        width: '900px',
        data: {
          ...this.dialogConfig.data,
          isFlow4Selection: true, // Indica que está no fluxo 4 para seleção de imagem
          context: 'flow4'
        },
      })
      dialogRef.afterClosed().subscribe(() => {
        this.assetGalleryService.getGalleryAssets().subscribe(
          (assetsList: AssetsItem[]) => {
            const assetSelected = assetsList.find(
              (el: AssetsItem) => el.selected,
            )
            this.geradorForm.get('step_5')?.get('imageSelected').setValue({
              id: assetSelected.id,
              bucket_id: assetSelected.uuid,
              selected: true,
              url: assetSelected.src,
              type: 0,
            })
            this.changeValue()
            this.saveAllStepsData()
            this.service.setCurrentStep(5)
          },
          error => {
            console.error('Ocorreu um erro: ' + error)
          },
        )
      })
    } else if (option === 'criar') {
      this.currentStep$.subscribe(step => {
        if (step && step.step === 4) {
          step.component.subtitle = 'Gerar Imagem'
          this.footerAction.showFooter()
        }
      })
    }

  }

  updateStep4Title(step) {
    if (!this.selectedOptionMethodImage) {
      step.component.subtitle = 'Escolha uma das opções'
    }
  }
}
