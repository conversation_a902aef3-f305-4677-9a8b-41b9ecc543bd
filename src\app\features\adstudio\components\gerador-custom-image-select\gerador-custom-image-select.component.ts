import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core'
import GeradorIAImage from '../../models/gerador-ia-image.model'
import { FormGroup } from '@angular/forms'
import { GeradorDimensionsImages } from '../../models/gerador-dimensions.model'
import { DetailLayoutImagesGerador } from '../../models/images-layout-model'
import { StepsDataService } from '../../services/steps-data.service'

@Component({
  selector: 'app-gerador-custom-image-select',
  templateUrl: './gerador-custom-image-select.component.html',
  styleUrls: ['./gerador-custom-image-select.component.scss'],
})
export class GeradorCustomImageSelectComponent implements OnInit {
  @Input() image: GeradorIAImage
  @Input() geradorForm: FormGroup
  @Output() selectImage: EventEmitter<GeradorIAImage> = new EventEmitter()
  @Output() updateLayoutImage: EventEmitter<DetailLayoutImagesGerador> =
    new EventEmitter()
  dataImageDimension: GeradorDimensionsImages
  detailsImageLayout: DetailLayoutImagesGerador

  stepOneData = { layout: '' }
  stepTwoData = { logo: '' }
  stepThreeData = {
    mainText: '',
    ctaText: '',
    layoutFirstColor: '',
    layoutSecondColor: '',
    layoutThirdColor: '',
  }

  stepFiveData = {
    imageSelected: {
      url: '',
    },
  }
  stepSixData = {
    layoutSelected: null,
  }

  constructor(private stepDataService: StepsDataService) {}

  ngOnInit(): void {
    this.stepDataService.stepsData$.subscribe(data => {
      const step2Data = data['step_2'] || {}
      const step3Data = data['step_3'] || {}
      const step5Data = data['step_5'] || {}

      this.detailsImageLayout = {
        textAds: this.getCurrentText(step3Data?.mainText),
        textButton: this.getCurrentText(step3Data?.ctaText),
        colorText: step3Data?.layoutSecondColor,
        colorBackground: step3Data?.layoutFirstColor,
        colorDetail: step3Data?.layoutThirdColor,
        width: this.chekWidthImage(),
        height: this.chekHeightImage(),
        sourceImage: step5Data?.imageSelected?.url,
        sourceLogo: step2Data?.logo,
        colorBackgroundLogo: step2Data?.colorLogo,
      }
    })

    if (this.geradorForm) {
      this.stepOneData = this.geradorForm.get('step_1')?.get('layout')?.value
    }
  }

  onSelectChange(image): void {
    this.selectImage.emit(image)
  }

  handleClick(image): void {
    this.selectImage.emit(image)
  }

  chekWidthImage() {
    switch (this.stepOneData?.layout) {
      case 'Billboard':
        return 970
      case 'Retângulo Medio':
        return 300
      case 'Half-Page':
        return 600
      default:
        return 970
    }
  }
  chekHeightImage() {
    switch (this.stepOneData?.layout) {
      case 'Billboard':
        return 250
      case 'Retângulo Medio':
        return 300
      case 'Half-Page':
        return 600
      default:
        return 250
    }
  }

  private getCurrentText(textData): string {
    if (
      textData &&
      Array.isArray(textData.texts) &&
      typeof textData.currentIndex === 'number'
    ) {
      return textData.texts[textData.currentIndex] || 'Texto Padrão'
    }
    return 'Texto Padrão'
  }
}
