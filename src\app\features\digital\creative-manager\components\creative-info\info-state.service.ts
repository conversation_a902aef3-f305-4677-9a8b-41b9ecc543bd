import { Injectable } from '@angular/core'
import {
  AssetsItem,
  CreativeItem,
  InfoItem,
} from '@app/core/services/no-auth/digital/model/digital-interface-utils'
import { SlideCardStateService } from '@app/shared/components/slide-card/slide-card-state/slide-card-state.service'
import { BehaviorSubject, Observable, Subject } from 'rxjs'
import { map } from 'rxjs/operators'

@Injectable({
  providedIn: 'root',
})
export class InfoStateService {
  private infoData = new BehaviorSubject<InfoItem>(null)
  private deleteEvent = new Subject()

  constructor(private slideCardStateService: SlideCardStateService) {}

  setCreativeInfo(data: CreativeItem) {
    this.infoData.next({
      type: 'creative',
      data: data,
    })
    this.slideCardStateService.openModal('creative')
  }

  setAssetsInfo(data: AssetsItem) {
    this.infoData.next({
      type: 'asset',
      data: data,
    })
    this.slideCardStateService.openModal('asset')
  }

  get getInfoData$(): Observable<InfoItem> {
    return this.infoData.asObservable()
  }

  clearInfo() {
    this.infoData.next(null)
    this.slideCardStateService.closeModal()
  }

  setDeleteEvent() {
    this.deleteEvent.next('event')
  }

  get deleteEvent$(): Observable<any> {
    return this.deleteEvent.asObservable()
  }

  get getCreativeInfo$(): Observable<CreativeItem> {
    return this.infoData
      .asObservable()
      .pipe(
        map(item =>
          item && item.type === 'creative'
            ? (item.data as CreativeItem)
            : new CreativeItem(),
        ),
      )
  }

  get getAssetInfo$(): Observable<AssetsItem> {
    return this.infoData
      .asObservable()
      .pipe(
        map(item =>
          item && item.type === 'asset' ? (item.data as AssetsItem) : null,
        ),
      )
  }
}
