@import '_colors';
@import '_breakpoints';

app-gerador-custom-input,
app-gerador-custom-text-generator,
app-gerador-preview,
app-gerador-color-pallete {
  width: 100%;
}

.gerador-form-container {
  .title-icon {
    width: 24px;
    height: 24px;
    background-size: cover;
    background-position: center;
    margin-right: 8px;

    &.step-1 {
      background-image: url('../../assets/icons/formato-icon.svg');
    }

    &.step-2 {
      background-image: url('../../assets/icons/business.svg');
    }

    &.step-3 {
      background-image: url('../../assets/icons/texto-icon.svg');
    }

    &.step-4 {
      background-image: url('../../assets/icons/magic-icon-black.svg');
    }

    &.step-5 {
      background-image: url('../../assets/icons/image-icon.svg');
    }

    &.step-6 {
      background-image: url('../../assets/icons/hand-icon.svg');
    }
  }

  .gerador-form-title {
    padding-bottom: 16px;
  }

  app-gerador-custom-select {
    width: 48%;
    margin-bottom: 16px;

    @include break-below(xs) {
      width: 45%;
      margin-bottom: 0px;
      .description {
        font-size: 12px;
      }
    }
  }

  .hidden {
    display: none !important;
  }

  .second-disc {
    .info-icon {
      width: 30px;
      height: 30px;
      background-position: center;
      background-repeat: no-repeat;
      background-image: url('../../assets/icons/info.svg');
    }
  }

  app-gerador-card-toggle {
    width: 48%;
  }

  app-gerador-custom-image-select {
    width: 50%;
  }

  @media (max-width: 768px) {
    app-gerador-card-toggle {
      width: 50%;
    }

    app-gerador-custom-select {
      width: 48%;
    }
  }

  .gerador-form-body {
    @media (max-width: 768px) {
      width: 100%;
      gap: 12px;
    }
  }
  .gerador-form-body p {
    color: #616161;
    font-size: 1rem;
    line-height: 1rem;
    font-family: 'Inter', sans-serif !important;
    font-size: 14px;
  }

  .finish-list-controls {
    button {
      width: 100%;
      margin-bottom: 16px;
    }
  }
}
::ng-deep .el-checkbox__input.is-checked .el-checkbox__inner {
  background: linear-gradient(90deg, #8800f8, #4300f8);
  border-color: transparent;
}

::ng-deep .el-checkbox__input.is-checked .el-checkbox__inner:hover {
  background: linear-gradient(90deg, #8800f8, #4300f8);
}

::ng-deep .el-checkbox__input.is-checked .el-checkbox__inner::after {
  content: '';
  display: inline-block;
  width: 8px;
  height: 8px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
  position: absolute;
  top: 30%;
  left: 50%;
  transform: translate(-50%, -50%) rotate(45deg);
}

::ng-deep .el-checkbox.is-checked .el-checkbox__label {
  color: #3a3a3a;
}

::ng-deep .el-checkbox__input.is-checked + .el-checkbox__label {
  color: #3a3a3a;
}
::ng-deep .el-checkbox__inner {
  border-radius: 4px;
  width: 20px;
  height: 20px;
}
.criativos-list {
  &.half-page-list {
    flex-direction: row;
    display: flex;
    flex-wrap: wrap;
  }
  @media (max-width: 768px) {
    width: 100%;
  }
}

.loading-btn {
  margin: 50px 0 20px 80px !important;
}

.loading-icon {
  margin: 0 0 0 80px !important;
}

.icon-check {
  background-image: url('../../assets/icons/checkbox-icon.svg');
  width: 20px;
  height: 20px;
  background-position: center;
  position: absolute;
  top: 57px;
  left: 93%;
  &.half-page-check {
    left: 84%;
    @include break-below(xs) {
      left: 83%;
    }
  }
}
