<div class="gerador-form-container" *ngIf="currentStep$ | async as currentStep">
  <div class="gerador-form-title d-flex align-items-center">
    <div class="title-icon" [ngClass]="'step-' + currentStep.step"></div>
    <h3>{{ currentStep.component.subtitle }}</h3>
  </div>

  <form [formGroup]="geradorForm" #formDirective>
    <div
      class="gerador-form-body d-flex flex-wrap justify-content-between"
      *ngIf="currentStep.step === 1"
    >
      <ng-container *ngFor="let layout of step_1">
        <app-gerador-custom-select
          [layout]="layout"
          (click)="handleSelect(layout)"
          [geradorForm]="geradorForm"
        ></app-gerador-custom-select>
      </ng-container>
    </div>

    <div
      class="gerador-form-body d-flex flex-wrap font-book"
      *ngIf="currentStep.step === 2"
    >
      <ng-container *ngFor="let input of step_2">
        <app-gerador-custom-input
          [currentStep]="currentStep"
          [inputAtts]="input"
          [geradorForm]="geradorForm"
          (changeValue)="changeValue()"
        >
          <dinamic-error-box *ngIf="erroAoGerarTexto">
            <app-gerador-disclaimer
              [disclaimerObj]="disclaimers[indexDisclaimer]"
            >
            </app-gerador-disclaimer>
          </dinamic-error-box>
        </app-gerador-custom-input>
      </ng-container>

      <app-gerador-disclaimer
        [disclaimerObj]="disclaimers[0]" iconSrc="info.svg"
      ></app-gerador-disclaimer>
    </div>

    <div
      class="gerador-form-body d-flex flex-wrap font-book"
      *ngIf="currentStep.step === 3"
    >
      <app-gerador-disclaimer
        iconSrc="magic-grey.svg"
        [disclaimerObj]="disclaimers[3]"
      ></app-gerador-disclaimer>
      <ng-container *ngFor="let input of step_3">
        <app-gerador-custom-text-generator
          [inputAtts]="input"
          [geradorForm]="geradorForm"
          (notify)="notifyError($event)"
          [currentStep]="currentStep"
        ></app-gerador-custom-text-generator>
      </ng-container>
      <app-gerador-preview
        [textFormAttrs]="step_3"
        [colorsFormAttrs]="colorsFormAttrs"
      ></app-gerador-preview>
      <app-gerador-color-pallete
        (changeValue)="changePalleteValue()"
        [colorsFormAttrs]="colorsFormAttrs"
      ></app-gerador-color-pallete>
    </div>

    <div
      class="gerador-form-body d-flex flex-wrap font-book flex-column"
      *ngIf="currentStep.step === 4"
    >
      <!-- Section para a pré seleção da forma como será ateribuida uma imagem para o criativo -->

      <div class="w-full px-4 cursor-pointer">
        <div
          *ngIf="
            !selectedOptionMethodImage ||
            selectedOptionMethodImage === 'minhasImagens'
          "
          class="w-full space-y-4"
        >
          <app-option-card
            *ngFor="let option of imageMethodOptions"
            [title]="option.label"
            [description]="option.description"
            [iconSrc]="option.icon"
            [isSelected]="selectedOptionMethodImage === option.value"
            (optionClick)="onOptionClickImageSelection(option.value)"
          >
          </app-option-card>
        </div>
      </div>

      <!-- Fluxo original de geração de imagem -->
      <div
        *ngIf="selectedOptionMethodImage === 'criar'"
        class="generate-image-mode"
      >
        <!-- Conteúdo existente para geração de imagem -->
        <p>Tipo de imagem</p>
        <div class="image-type d-flex justify-content-between">
          <ng-container *ngFor="let imgType of imageType">
            <app-gerador-card-toggle
              [currentStep]="currentStep"
              [imgData]="imgType"
              (selectCard)="handleSelectCard($event)"
            ></app-gerador-card-toggle>
          </ng-container>
        </div>

        <p>Fundo da imagem</p>
        <div class="image-type d-flex justify-content-between">
          <ng-container *ngFor="let imgType of backgroundType">
            <app-gerador-card-toggle
              [currentStep]="currentStep"
              [imgData]="imgType"
              (selectCard)="handleSelectCard($event)"
            ></app-gerador-card-toggle>
          </ng-container>
        </div>

        <div class="gerador-image-body">
          <ng-container *ngIf="imageType[0].selected; else withObj">
            <div *ngFor="let input of step_4">
              <app-gerador-custom-input
                [currentStep]="currentStep"
                [inputAtts]="input"
                [geradorForm]="geradorForm"
                (changeValue)="changeValue()"
              >
              </app-gerador-custom-input>
            </div>
          </ng-container>

          <ng-template #withObj>
            <app-gerador-custom-input
              [currentStep]="currentStep"
              [inputAtts]="objTypeForm[0]"
              [geradorForm]="geradorForm"
              (changeValue)="changeValue()"
            >
            </app-gerador-custom-input>
          </ng-template>
        </div>

        <ng-container *ngIf="erroAoGerarImage">
          <app-gerador-disclaimer
            [disclaimerObj]="disclaimers[indexDisclaimer]"
          >
          </app-gerador-disclaimer>
        </ng-container>

        <app-gerador-color-pallete
          (changeValue)="changePalleteValue($event)"
          [colorsFormAttrs]="imageColors"
          [title]="'Cores da imagem'"
          [id]="1"
        ></app-gerador-color-pallete>
      </div>
    </div>

    <div
      class="gerador-form-body d-flex flex-wrap font-book flex-column"
      *ngIf="currentStep.step === 5"
    >
      <div
        class="finish-list-container d-flex justify-content-between flex-wrap"
        *ngIf="step_5.length > 0"
      >
        <ng-container *ngFor="let image of step_5">
          <app-gerador-custom-image-select
            [image]="image"
            (selectImage)="selectImage($event)"
            (updateLayoutImage)="updateLayoutImage($event)"
          ></app-gerador-custom-image-select>
        </ng-container>
      </div>

      <div
        class="finish-list-container d-flex flex-column justify-content-center align-items-center gap-4"
        *ngIf="step_5.length == 0 || gerandoImagem"
      >
        <app-skeleton-loader
          [count]="2"
          [width]="'260px'"
          [height]="'260px'"
          [borderRadius]="'8px'"
          [direction]="'row'"
        ></app-skeleton-loader>
        <app-skeleton-loader
          [count]="2"
          [width]="'260px'"
          [height]="'260px'"
          [borderRadius]="'8px'"
          [direction]="'row'"
        ></app-skeleton-loader>
      </div>

      <app-gerador-disclaimer
        [disclaimerObj]="disclaimers[1]"
      ></app-gerador-disclaimer>

      <div class="mt-3 mb-3" *ngIf="step_5.length > 0">
        <el-checkbox formControlName="saveAllImages">
          Salvar imagens em <u>Minhas imagens.</u>
        </el-checkbox>
      </div>

      <div class="finish-list-controls">
        <button
          glbButton
          [disabled]="checkMaxGerados()"
          theme="purple-gradient-secondary-gerador"
          class="c-dialog-display-format__btn"
          (click)="gerarMais()"
          data-element="button"
          data-state="activated"
          data-area="gerador_criativos"
          data-section="5_finalizacao_gerar_mais_opcoes"
          data-label="gerar_mais_opcoes"
          *ngIf="step_5.length > 0"
        >
          Gerar mais opções
        </button>
      </div>

      <app-gerador-disclaimer
        *ngIf="checkMaxGerados()"
        [disclaimerObj]="disclaimers[5]"
        [analitcsMetaData]="limiteAtingidoMetaDados"
      ></app-gerador-disclaimer>
    </div>

    <div
      class="gerador-form-body d-flex flex-wrap font-book flex-column"
      *ngIf="currentStep.step === 6"
    >
      <div
        class="criativos-list"
        [ngClass]="{ 'half-page-list': halfpageWrap }"
      >
        <ng-container *ngFor="let image of step_6">
          <app-gerador-custom-image-select
            [image]="image"
            (selectImage)="selectImage($event, 2)"
            [geradorForm]="geradorForm"
          ></app-gerador-custom-image-select>
        </ng-container>
      </div>

      <button
        *ngIf="!cameFromAssets"
        glbButton
        theme="purple-gradient-secondary-gerador"
        class="c-dialog-display-format__btn"
        (click)="goToBackStep()"
        data-element="button"
        data-state="activated"
        data-area="gerador_criativos"
        data-section="6_finalizacao_escolher_outra_opcao"
        data-label="escolher_outra_imagem"
      >
        Escolher outra imagem
      </button>
    </div>
  </form>
</div>
