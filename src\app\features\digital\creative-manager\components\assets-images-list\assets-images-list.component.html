<div class="container mx-auto px-4 py-6 container-assets-images-list">
  <app-info-bar></app-info-bar>
  <div class="flex flex-col md:flex-row justify-between mb-6 gap-2 md:gap-0">
    <div
      class="flex flex-wrap gap-2 items-center"
      [ngClass]="{ hidden: selectedCount > 0, block: selectedCount === 0 }"
    >
      <adtech-typography
        variant="body"
        size="small"
        styles="!text-content-secondary-light"
        >Filtrar por:</adtech-typography
      >

      <div class="label-container w-full lg:min-w-[200px] lg:max-w-fit">
        <app-globo-sim-form-field>
          <app-multi-select-check
            type="single"
            [selectStyles]="selectCustom"
            placeHolder="todos os filtros"
            [listOfValues]="formatOptions"
            (selectedValuesChange)="handleSelectFilter()"
          ></app-multi-select-check>
        </app-globo-sim-form-field>
      </div>

      <div
        class="label-container w-full lg:min-w-[128px] lg:max-w-fit !py-2 !px-3 !h-auto !border border-border-neutral-light !rounded-lg"
      >
        <button
          class="w-full d-flex justify-between"
          (click)="toggleFavoriteFilter()"
        >
          <adtech-typography
            variant="body"
            size="small"
            styles="!text-content-secondary-light"
            >favoritos</adtech-typography
          >
          <img
            *ngIf="filterByFavoritos; else notFavoriteIcon"
            vaultSrc="icons/favorite_full.svg"
            alt="Favorited"
            class="h-6 w-6 transition-transform duration-300 ease-in-out scale-110"
          />
          <ng-template #notFavoriteIcon>
            <img
              vaultSrc="icons/favorite.svg"
              alt="Not favorited"
              class="h-6 w-6 transition-transform duration-300 ease-in-out"
            />
          </ng-template>
        </button>
      </div>
    </div>

    <div class="w-full md:w-auto md:flex-grow">
      <div
        [ngClass]="{ hidden: selectedCount === 0, block: selectedCount > 0 }"
        class="flex flex-wrap items-center gap-y-2"
      >
        <span class="text-sm font-medium mr-2"
          >{{ selectedCount }} imagens selecionadas:</span
        >

        <button
          class="text-indigo-600 text-sm font-medium mr-4 mt-1 md:mt-0"
          *ngIf="selectedCount === 1"
          (click)="useOnNewCreative()"
        >
          <div class="flex items-center">
            <img src="../../../../../../assets/imgs/icons/creator-magic.svg" />
            <span class="ml-1">Usar em um novo criativo</span>
          </div>
        </button>

        <button
          class="text-red-500 text-sm font-medium mt-1 md:mt-0"
          (click)="deleteMutipleAsset()"
        >
          <div class="flex items-center">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-4 w-4 mr-1"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fill-rule="evenodd"
                d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z"
                clip-rule="evenodd"
              />
            </svg>
            <span>Excluir imagem(ns)</span>
          </div>
        </button>
      </div>
    </div>

    <div
      class="flex items-center text-gray-700 text-sm bg-[#f5f5f7] rounded-md p-2 w-full md:w-auto md:ml-auto self-end md:self-auto"
    >
      <div
        class="w-6 h-6 rounded-full bg-white border border-gray-200 flex items-center justify-center mr-2"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-4 w-4 text-indigo-600"
          viewBox="0 0 20 20"
          fill="currentColor"
        >
          <path
            fill-rule="evenodd"
            d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z"
            clip-rule="evenodd"
          />
        </svg>
      </div>
      <span
        >({{ favoriteCount }}/{{ 15 }}) Imagens marcadas como favoritas não têm
        prazo de validade.</span
      >
    </div>
  </div>
  <ng-container *ngIf="loadingImages; else content">
    <ng-container *ngTemplateOutlet="loadingCards"></ng-container>
  </ng-container>

  <ng-template #content>
    <ng-container *ngIf="(images$ | async)?.length > 0; else noContent">
      <div class="flex flex-wrap gap-[12px] justify-content-between">
        <app-assets-card
          *ngFor="let image of images$ | async"
          [image]="image"
          [canFavorite]="favoriteCount < 15 || image.favorite"
          [isFlow4Selection]="isFlow4Selection"
          (onSelectImage)="toggleSelect(image)"
          (onFavoriteImage)="toggleFavorite(image)"
          (onMoreOptions)="handleMoreOptions(image)"
          (onDeleteAsset)="handleDelete(image.uuid)"
        >
        </app-assets-card>
      </div>
    </ng-container>
  </ng-template>

  <div class="flex justify-center mt-10">
    <button
      class="mx-auto"
      glbButton
      theme="secondary"
      (click)="loadMore()"
      *ngIf="hasMoreImages && !loadingImages && !showEmptyState"
    >
      Ver mais
    </button>
  </div>
</div>

<ng-template #noContent>
  <app-no-content
    *ngIf="showEmptyState; else loadingCards"
    [type]="filterByFavoritos ? 'favorites' : 'all'"
  ></app-no-content>
</ng-template>

<ng-template #loadingCards>
  <div class="d-flex flex-column">
    <app-skeleton-loader
      [count]="3"
      [width]="'265px'"
      [height]="'150px'"
      [borderRadius]="'8px'"
      [direction]="'row'"
    ></app-skeleton-loader>
    <div class="mt-2"></div>
    <app-skeleton-loader
      [count]="3"
      [width]="'265px'"
      [height]="'150px'"
      [borderRadius]="'8px'"
      [direction]="'row'"
    ></app-skeleton-loader>
  </div>
</ng-template>
