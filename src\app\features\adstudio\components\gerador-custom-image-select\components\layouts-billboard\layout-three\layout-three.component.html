<div
  class="layout-container"
  [style.border]="data.border || '1px solid black'"
  [style.backgroundColor]="data.colorBackground"
>
  <app-watter-mark></app-watter-mark>
  <div class="content-text-logo">
    <div class="layout-text" [style.color]="data.colorText">
      {{ data.textAds }}
    </div>
  </div>

  <div class="content-image-and-button">
    <img [src]="data.sourceImage" alt="Imagem do layout" class="layout-data" />
  </div>
  <div class="content-logo">
    <div
      class="layout-content-detail"
      [style.backgroundColor]="data.colorDetail"
    ></div>
    <div
      class="layout-logo-container"
      [style.display]="'flex'"
      [style.flexDirection]="'column'"
      [style.alignItems]="'center'"
      [style.justifyContent]="'center'"
    >
      <div>
        <div
          class="layout-logo-container-child"
          [style.borderRadius]="'8px'"
          [style.backgroundColor]="data.colorBackgroundLogo || '#ffffff'"
          [style.height]="'50px'"
          [style.width]="'50px'"
          [style.display]="'flex'"
          [style.alignItems]="'center'"
          [style.justifyContent]="'center'"
          [style.overflow]="'hidden'"
          [style.padding]="'2px'"
        >
          <img
            [src]="data.sourceLogo"
            [alt]="'Logo'"
            [style.maxWidth]="'80%'"
            [style.maxHeight]="'80%'"
            [style.objectFit]="'contain'"
          />
        </div>
      </div>
      <button
        class="layout-button"
        [style.color]="data.colorText"
        [style.backgroundColor]="data.colorBackground"
        [style.border]="'2px solid ' + data.colorText"
      >
        {{ data.textButton }}
      </button>
    </div>
    <div
      class="layout-content-detail"
      [style.backgroundColor]="data.colorDetail"
    ></div>
  </div>
</div>
