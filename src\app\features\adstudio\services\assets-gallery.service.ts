import { Injectable } from '@angular/core'
import { BehaviorSubject, Observable } from 'rxjs'
import {
  AssetRequest,
  AssetsItem,
} from '@app/core/services/no-auth/digital/model/digital-interface-utils'
import { HttpClient, HttpHeaders } from '@angular/common/http'
import { environment } from '@env/environment'
import { NotificationService } from '@app/core/services/notification/notification.service'
import { LocalStorageService } from '@app/core/services/local-storage/local-storage.service'
import { StorageKeysEnum } from '@app/shared/models/enums/storage-keys.enum'

@Injectable({
  providedIn: 'root',
})
export class AssetsGalleryService {
  constructor(
    private http: HttpClient,
    private notificationService: NotificationService,
    private localStorageService: LocalStorageService,
  ) {}

  private galleryAssets = new BehaviorSubject<AssetsItem[]>([])
  private urlGallery = environment.api.adstudio

  getGalleryAssets(): Observable<AssetsItem[]> {
    return this.galleryAssets.asObservable()
  }

  updateGalleryAssets(gallery: AssetsItem[]): void {
    this.galleryAssets.next(gallery)
  }

  uploadAsset(assetList: AssetRequest[]) {
    return this.http.post<string>(this.urlGallery.baseAssets, assetList)
  }

  deleteAsset(assetList: string[]) {
    return this.http
      .delete<string>(this.urlGallery.baseAssets, {
        body: assetList,
      })
      .subscribe(
        _ => {
          this.notificationService.showToastSuccess(
            'Imagen(s) excluídas com sucesso!',
          )
        },
        error => console.error('Erro ao excluir asset: ' + error),
      )
  }

  postFavoriteAsset(idAsset: string, favorite: boolean): Observable<string> {
    const authToken = this.localStorageService.get<string>(
      StorageKeysEnum.authToken,
    )
    const headers = new HttpHeaders({
      Authorization: 'Bearer ' + authToken,
    })
    const favoriteUrl = this.urlGallery.baseAssets + '/' + idAsset + '/favorite'
    const favoriteBody = {
      favorite,
    }
    return this.http.post<string>(favoriteUrl, favoriteBody, {
      headers,
    })
  }
}
