.layout-container {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  height: 106px;
  max-height: 120px;
  overflow: hidden;
  font-family: Arial, sans-serif;
  background-color: #fdf6e3;
  width: 93%;
  box-sizing: border-box;
  position: relative;
  overflow: hidden;
}

.layout-content-text-logo {
  display: flex;
  flex-direction: row;
  align-items: center;
  flex: 1;
  padding: 0 16px;
}

.layout-text {
  font-size: 15px;
  width: 180px;
  font-weight: bold;
  color: #5a4634;
  line-height: 24px;
  height: 72px;
  display: flex;
  align-items: center;
  padding-left: 1rem;
}

.layout-logo-container {
  display: flex;
  align-items: center;
  justify-content: center;
}

.layout-logo {
  width: 46px;
  height: 46px;
  border-radius: 8px;
  object-fit: contain;
}

.layout-content-detail {
  width: 6px;
  height: 100%;
  background-color: #f4a261;
  flex-shrink: 0;
}

.content-image-and-button {
  position: relative;
  width: 142px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  height: 100%;
}

.layout-data {
  width: 100%;
  height: 100%;
  object-fit: fill;
}

.layout-button {
  margin-top: 10px;
  font-weight: bold;
  color: #fff;
  background-color: #5a4634;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 65px;
  height: 18px;
  font-size: 8px;
  line-height: 9.68px;
  border: 1px solid white;
}

/* Responsividade */
@media (max-width: 768px) {
  .layout-container {
    flex-direction: row;
    height: auto;
    width: 95%;
  }

  .layout-text {
    font-size: 12px;
  }

  .layout-logo {
    width: 40px;
    height: 40px;
  }

  .layout-button {
    font-size: 10px;
  }
}

@media (max-width: 480px) {
  .layout-logo-container-child {
    width: 40px !important;
    height: 40px !important;
  }
  .layout-container {
    flex-wrap: nowrap;
    height: 86px;
  }

  .layout-content-text-logo {
    // gap: 8px;
    padding: 0 8px;
    max-width: 195px;
  }

  .layout-text {
    font-size: 12px !important;
    line-height: 14px !important;
  }

  .layout-logo {
    width: 30px;
    height: 30px;
  }

  .layout-button {
    font-size: 8px;
  }

  .container-logo {
    height: 30px !important;
    width: 30px !important;
    border-radius: 4px !important;
  }
  .layout-button {
    min-width: 75px;
  }
}

.billboard {
  width: 970px;
  height: 250px;

  .type {
    height: 250px;

    &_2 {
      height: 250px;
      display: flex;
      justify-content: space-between;

      .c-1 {
        display: flex;
        justify-content: flex-start;
        align-items: end;
        background-size: cover;
        background-position: center;
        background-repeat: no-repeat;
        width: 330px;

        .image {
          button {
            margin-bottom: 20px;
            margin-left: 24px;
            outline: none;
          }
        }
      }

      .c-2 {
        width: 617px;
        background-color: #fc560c;
        display: flex;
        justify-content: space-around;
        align-items: center;
        background-color: #50201a;
        vertical-align: middle;
        margin: 0;

        p {
          color: #50201a;
        }
      }
    }
  }

  .logo {
    width: 120px;
    height: 120px;
    margin: 30px;
    border-radius: 24px;
    background-color: transparent;
    background-size: cover;
    background-position: center;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  button {
    width: 141px;
    height: 39px;
    border-radius: 6px;
    border: 2px solid white;
    color: white;
    background-color: #fc560c;
    font-weight: 600;
    font-size: 1rem;
  }

  .image {
    background-size: cover;
  }

  .text {
    padding-left: 24px;
    p {
      font-weight: 600;
      width: 420px;
      max-width: 420px;
      overflow: hidden;
      height: 162px;
      font-size: 40px;
      color: white;
      line-height: 48px;
    }
  }
}
