import {
  Directive,
  ElementRef,
  Renderer2,
  HostListener,
  Input,
  OnInit,
} from '@angular/core'
import { HttpClient } from '@angular/common/http'

@Directive({
  selector: '[appPopUpMenu]',
})
export class PopUpMenuDirective implements OnInit {
  @Input() menuItems: {
    label: string
    action: () => void
    iconFilePath?: string
    iconColor?: string
    iconSize?: string
    labelColor?: string
    fontStyle?: string
    fontWeight?: string
    fontSize?: string
    labelGradient?: boolean
    marginRight?: string
  }[] = []

  @Input() menuSide: 'left' | 'right' = 'left' // Default to left side
  private menuElement: HTMLElement | null = null

  constructor(
    private el: ElementRef,
    private renderer: Renderer2,
    private http: HttpClient,
  ) {}

  ngOnInit() {
    this.menuItems.forEach(item => {
      if (item.iconFilePath) {
        this.loadSVGIcon(item.iconFilePath).then(svgElement => {
          item['svgElement'] = svgElement
        })
      }
    })
  }

  @HostListener('click') onClick() {
    if (this.menuElement) {
      this.destroyMenu()
    } else {
      this.createMenu()
    }
  }

  async loadSVGIcon(filePath: string): Promise<SVGElement> {
    const svgText = await this.http
      .get(filePath, { responseType: 'text' })
      .toPromise()
    const parser = new DOMParser()
    const svgDoc = parser.parseFromString(svgText, 'image/svg+xml')
    return svgDoc.querySelector('svg') as SVGElement
  }

  createMenu() {
    this.menuElement = this.renderer.createElement('div')
    this.renderer.setStyle(this.menuElement, 'position', 'absolute')
    this.renderer.setStyle(this.menuElement, 'backgroundColor', '#fff')
    this.renderer.setStyle(
      this.menuElement,
      'boxShadow',
      '0px 4px 6px rgba(0, 0, 0, 0.1)',
    )
    this.renderer.setStyle(this.menuElement, 'borderRadius', '16px')
    this.renderer.setStyle(this.menuElement, 'zIndex', '1000')

    const { top, left, right, height } =
      this.el.nativeElement.getBoundingClientRect()
    const scrollTop = window.scrollY
    const scrollLeft = window.scrollX

    this.renderer.setStyle(
      this.menuElement,
      'top',
      `${top + height + scrollTop - 30}px`,
    )

    if (this.menuSide === 'left') {
      this.renderer.setStyle(this.menuElement, 'left', `${left + scrollLeft}px`)
    } else if (this.menuSide === 'right') {
      this.renderer.setStyle(
        this.menuElement,
        'right',
        `${window.innerWidth - right + scrollLeft + 30}px`,
      )
    }

    this.menuItems.forEach((item, index) => {
      const menuItem = this.renderer.createElement('div')
      this.renderer.setStyle(menuItem, 'width', '220px')
      this.renderer.setStyle(menuItem, 'height', '52px')
      this.renderer.setStyle(menuItem, 'padding', '16px 20px')
      this.renderer.setStyle(menuItem, 'display', 'flex')
      this.renderer.setStyle(menuItem, 'alignItems', 'center')
      this.renderer.setStyle(menuItem, 'gap', '8px')
      this.renderer.setStyle(menuItem, 'cursor', 'pointer')
      this.renderer.setAttribute(menuItem, 'data-element', 'button')
      this.renderer.setAttribute(menuItem, 'data-state', 'activated')
      this.renderer.setAttribute(menuItem, 'data-area', 'meus_anuncios')
      this.renderer.setAttribute(menuItem, 'data-section', 'criativos')
      this.renderer.setAttribute(menuItem, 'data-label', item.label)

      if (index !== this.menuItems.length - 1) {
        this.renderer.setStyle(menuItem, 'borderBottom', '1px solid #EBEBEB')
      }

      if (item['svgElement']) {
        const svg = item['svgElement'].cloneNode(true) as SVGElement
        this.renderer.setStyle(svg, 'width', item.iconSize || '18px')
        this.renderer.setStyle(svg, 'height', item.iconSize || '18px')
        if (item.iconColor) {
          this.renderer.setStyle(svg, 'fill', item.iconColor)
        }
        this.renderer.setStyle(svg, 'marginRight', item.marginRight || '8px')
        this.renderer.setAttribute(svg, 'data-element', 'button')
        this.renderer.setAttribute(svg, 'data-state', 'activated')
        this.renderer.setAttribute(svg, 'data-area', 'meus_anuncios')
        this.renderer.setAttribute(svg, 'data-section', 'criativos')
        this.renderer.setAttribute(svg, 'data-label', item.label)
        this.renderer.appendChild(menuItem, svg)
      }

      const labelElement = this.renderer.createElement('span')
      const text = this.renderer.createText(item.label)
      this.renderer.setAttribute(labelElement, 'data-element', 'button')
      this.renderer.setAttribute(labelElement, 'data-state', 'activated')
      this.renderer.setAttribute(labelElement, 'data-area', 'meus_anuncios')
      this.renderer.setAttribute(labelElement, 'data-section', 'criativos')
      this.renderer.setAttribute(labelElement, 'data-label', item.label)

      this.renderer.setStyle(labelElement, 'color', item.labelColor || '#000')
      this.renderer.setStyle(
        labelElement,
        'fontStyle',
        item.fontStyle || 'normal',
      )
      this.renderer.setStyle(
        labelElement,
        'fontWeight',
        item.fontWeight || 'normal',
      )
      this.renderer.setStyle(labelElement, 'fontSize', item.fontSize || '14px')

      this.renderer.appendChild(labelElement, text)
      this.renderer.listen(menuItem, 'click', () => {
        item.action()
        this.destroyMenu()
      })
      this.renderer.appendChild(menuItem, labelElement)

      this.renderer.appendChild(this.menuElement, menuItem)

      if (item.labelGradient) {
        this.renderer.addClass(labelElement, 'gradient-text-menu')
      } else {
        this.renderer.setStyle(labelElement, 'color', item.labelColor || '#000')
      }
    })

    this.renderer.appendChild(document.body, this.menuElement)
  }

  destroyMenu() {
    if (this.menuElement) {
      this.renderer.removeChild(document.body, this.menuElement)
      this.menuElement = null
    }
  }

  @HostListener('document:click', ['$event.target']) onOutsideClick(
    targetElement: HTMLElement,
  ) {
    if (
      this.menuElement &&
      !this.el.nativeElement.contains(targetElement) &&
      !this.menuElement.contains(targetElement)
    ) {
      this.destroyMenu()
    }
  }
}
