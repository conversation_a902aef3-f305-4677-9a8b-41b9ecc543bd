import { Component, Input, OnInit } from '@angular/core'
import { GeradorCriativosService } from '@app/features/adstudio/services/gerador-criativos.service'

@Component({
  selector: 'app-no-content',
  templateUrl: './no-content.component.html',
})
export class NoContentComponent implements OnInit {
  @Input() label: string
  @Input() type: 'favorites' | 'all'
  constructor(private geradorCriativosService: GeradorCriativosService) {}

  ngOnInit() {}

  goToGenerator() {
    this.geradorCriativosService.handlePanelState(true)
  }
}
