<div
  class="layout-container"
  [style.display]="'flex'"
  [style.flexDirection]="'row'"
  [style.alignItems]="'center'"
  [style.justifyContent]="'space-between'"
  [style.fontFamily]="'Arial, sans-serif'"
  [style.backgroundColor]="data.colorBackground || '#fdf6e3'"
  [style.width]="'93%'"
  [style.boxSizing]="'border-box'"
  [style.border]="data.border || '1px solid black'"
>
  <app-watter-mark></app-watter-mark>

  <div
    class="layout-content-text-logo"
    [style.display]="'flex'"
    [style.flexDirection]="'row'"
    [style.alignItems]="'center'"
    [style.flex]="'1'"
    [style.padding]="'0 16px'"
  >
    <div
      class="layout-text"
      [style.fontSize]="'15px'"
      [style.fontWeight]="'bold'"
      [style.color]="data.colorText || '#5a4634'"
      [style.lineHeight]="'24px'"
      [style.height]="'72px'"
      [style.display]="'flex'"
      [style.alignItems]="'center'"
    >
      {{ data.textAds }}
    </div>

    <div
      class="layout-logo-container"
      [style.display]="'flex'"
      [style.flexDirection]="'column'"
      [style.alignItems]="'center'"
      [style.justifyContent]="'center'"
    >
      <div
        class="layout-logo-container-child"
        [style.borderRadius]="'8px'"
        [style.backgroundColor]="data.colorBackgroundLogo || '#ffffff'"
        [style.height]="'50px'"
        [style.width]="'50px'"
        [style.display]="'flex'"
        [style.alignItems]="'center'"
        [style.justifyContent]="'center'"
        [style.overflow]="'hidden'"
        [style.padding]="'2px'"
      >
        <img
          [src]="data.sourceLogo"
          [alt]="'Logo'"
          [style.maxWidth]="'80%'"
          [style.maxHeight]="'80%'"
          [style.objectFit]="'contain'"
        />
      </div>

      <button
        class="layout-button"
        [style.fontWeight]="'bold'"
        [style.color]="data.colorText || '#fff'"
        [style.backgroundColor]="data.colorBackground || '#5a4634'"
        [style.border]="'2px solid ' + (data.colorText || '#fff')"
        [style.borderRadius]="'4px'"
        [style.cursor]="'pointer'"
        [style.display]="'flex'"
        [style.justifyContent]="'center'"
        [style.alignItems]="'center'"
        [style.height]="'18px'"
        [style.fontSize]="'8px'"
        [style.lineHeight]="'9.68px'"
      >
        {{ data.textButton }}
      </button>
    </div>
  </div>

  <div
    class="layout-content-detail"
    [style.width]="'6px'"
    [style.height]="'100%'"
    [style.backgroundColor]="data.colorDetail || '#f4a261'"
    [style.flexShrink]="'0'"
  ></div>

  <div
    class="content-image-and-button"
    [style.position]="'relative'"
    [style.width]="'142px'"
    [style.display]="'flex'"
    [style.alignItems]="'center'"
    [style.justifyContent]="'flex-end'"
    [style.height]="'100%'"
  >
    <img
      [src]="data.sourceImage"
      alt="Imagem do layout"
      class="layout-data"
      [style.width]="'100%'"
      [style.height]="'100%'"
      [style.objectFit]="'fill'"
    />
  </div>
</div>
