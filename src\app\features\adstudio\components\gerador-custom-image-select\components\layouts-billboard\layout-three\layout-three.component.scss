.layout-container {
  display: flex;
  flex-direction: row;
  align-items: center;
  box-sizing: border-box;
  justify-content: flex-end;
  height: 106px;
  max-height: 120px;
  overflow: hidden;
  font-family: Arial, sans-serif;
  background-color: #fdf6e3;
  width: 93%;
  position: relative;
  overflow: hidden;
}

.content-logo {
  display: block;
  width: 20%;
  height: 100%;
}
.layout-content-text-logo {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 16px;
  padding: 0 16px;
  width: 30%;
}

.content-text-logo {
  margin-right: 8px;
}

.layout-text {
  font-size: 15px;
  width: 180px;
  font-weight: bold;
  color: #5a4634;
  line-height: 24px;
  padding-left: 1rem;
  height: 72px;
  display: flex;
  align-items: center;
}

.layout-logo-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 86px;
}

.layout-logo {
  width: 46px;
  height: 46px;
  border-radius: 8px;
  object-fit: contain;
}

.layout-content-detail {
  width: 100%;
  height: 9px;
  background-color: #f4a261;
  flex-shrink: 0;
}

.content-image-and-button {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  height: 100%;
  width: 142px;
}

.layout-data {
  width: 100%;
  height: 100%;
  object-fit: fill;
}

.layout-button {
  margin-top: 6px;
  font-weight: bold;
  color: #fff;
  background-color: #5a4634;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  height: 39px;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 65px;
  height: 18px;
  font-size: 8px;
  line-height: 9.68px;
  border: 1px solid white;
}

/* Responsividade */
@media (max-width: 768px) {
  .layout-container {
    flex-direction: row;
    height: auto;
    width: 95%;
  }

  .layout-text {
    font-size: 12px;
  }

  .layout-logo {
    width: 40px;
    height: 40px;
  }

  .layout-button {
    font-size: 10px;
  }
}

@media (max-width: 480px) {
  .layout-container {
    flex-wrap: nowrap;
    height: 86px;
  }

  .layout-content-text-logo {
    gap: 8px;
    padding: 0 8px;
    max-width: 195px;
  }

  .layout-text {
    font-size: 12px !important;
    line-height: 14px !important;
    width: 120px;
    padding-left: 12px !important;
  }

  .layout-logo {
    width: 30px;
    height: 30px;
  }

  .layout-button {
    font-size: 8px;
  }

  .container-logo {
    height: 30px !important;
    width: 30px !important;
    border-radius: 4px !important;
  }
  .layout-button {
    min-width: 75px;
    right: 18px;
  }
  .layout-logo-container {
    min-height: 66px;
  }
  .layout-logo-container-child {
    width: 40px !important;
    height: 40px !important;
  }
}

.billboard {
  width: 970px;
  height: 250px;

  .type {
    height: 250px;

    &_3 {
      height: 250px;
      display: flex;
      justify-content: space-between;

      .c-1 {
        display: flex;
        align-items: center;
        width: 600px;
        p {
          color: #50201a;
        }
      }

      .c-2 {
        width: 330px;
        display: flex;
        justify-content: space-around;
        align-items: flex-end;
        background-size: cover;
        background-position: center;
        background-repeat: no-repeat;

        .image {
          button {
            margin-bottom: 20px;
            outline: none;
          }
        }
      }

      .c-3 {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        align-items: center;

        .line {
          width: 100%;
          height: 20px;
          background-color: #50201a;
        }
      }
    }
  }

  .logo {
    width: 120px;
    height: 120px;
    margin: 30px;
    border-radius: 24px;
    background-color: transparent;
    background-size: cover;
    background-position: center;
  }

  button {
    width: 141px;
    height: 39px;
    border-radius: 6px;
    border: 2px solid white;
    color: white;
    background-color: #fc560c;
    font-weight: 600;
    font-size: 1rem;
  }

  .image {
    background-size: cover;
  }

  .text {
    padding-left: 24px;
    p {
      font-weight: 600;
      width: 420px;
      max-width: 420px;
      overflow: hidden;
      height: 162px;
      font-size: 40px;
      color: white;
      line-height: 48px;
    }
  }
}
.image-container {
  position: relative;
  width: 100%;
  height: 100%;
}

.promo-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.promo-button {
  position: absolute;
  bottom: 15px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 2;
  padding: 10px 20px;
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  height: auto;
  min-height: 40px;
  border: 2px solid #000;
  background-color: #ffffff;
  color: #000000;
  white-space: nowrap;
  cursor: pointer;
  transition: all 0.3s ease;
}
