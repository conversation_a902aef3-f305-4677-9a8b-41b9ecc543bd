@import '../../../../../../../../assets/styles/colors';
@import '../../../../../../../../assets/styles/buttons';
@import '_breakpoints';

.dialog-chose-body,
.dialog-chose-header {
  overflow: hidden;
  padding: 24px;
}
.dialog-chose-body {
  padding-bottom: 0;
  margin-bottom: 0;
  @include break-below(xs) {
   display: block;
  }
}

#container-confirm-notices {
  max-width: 540px;
  display: flex;
  height: 100%;
  align-items: center;
  flex-direction: column;
  justify-content: space-between;
  text-align: center;

  h1 {
    color: black;
    font-weight: bold;
    font-size: 1.3rem;
    margin-bottom: 24px;
  }

  .card-opt {
    cursor: pointer;
    width: 226px;

    img {
      width: 100%;
      height: 138px;
      border-radius: 12px;
      margin: 0 auto;
      margin-bottom: 1rem;
    }
    p {
      line-height: 18px;
      font-size: 0.9rem;
      text-align: left;
      color: #616161;
    }
  }

  .color-black-60 {
    color: $color-black-60;
  }
  .color-blue-primary {
    color: $color-primary;
  }
  .container-close {
    width: 100%;
    display: flex;
    justify-content: flex-start;
    border-bottom: 1px solid #ebebeb;
  }
  mat-icon {
    min-height: 80px;
    width: auto;
    // margin: 20px 0;
  }
  .container-link-actions {
    cursor: pointer;
    &:hover {
      text-decoration: underline;
    }
  }
}

p {
  font-family: var(--Font-Family-Book, 'Inter Variable');
  font-size: 14px;
}
