import { Component, EventEmitter, Input, Output } from '@angular/core'
import { AssetsItem } from '@app/core/services/no-auth/digital/model/digital-interface-utils'
import { MultiSelectData } from '@app/shared/components/multi-select-check/model/multi-select-data'
import { FiltrosEnums } from '../../utils/filtros-galeria.enum'
import { AssetsItemService } from '../../services/assets-item.service'
import { AssetsFilter } from '../../model/assets.model'
import { AssetsGalleryService } from '@app/features/adstudio/services/assets-gallery.service'
import { MatDialog, MatDialogConfig } from '@angular/material/dialog'
import { DialogGenericAssetsComponent } from '../../dialogs/assets/dialog-generic-assets/dialog-generic-assets.component'
import { BehaviorSubject, Observable, timer } from 'rxjs'
import { take, switchMap } from 'rxjs/operators'
import { GeradorCriativosService } from '@app/features/adstudio/services/gerador-criativos.service'

@Component({
  selector: 'app-assets-images-list',
  templateUrl: './assets-images-list.component.html',
  styleUrls: ['./assets-images-list.component.scss'],
})
export class AssetsImagesListComponent {
  private imagesSubject = new BehaviorSubject<AssetsItem[]>([])
  @Output() onSelectAsset = new EventEmitter<AssetsItem[]>()
  @Input() isFlow4Selection: boolean = false
  dialogConfig = new MatDialogConfig()
  images$: Observable<AssetsItem[]> = this.imagesSubject.asObservable()
  selectedCount: number = 0
  favoriteCount: number = 0
  formatOptions: MultiSelectData[] = this.initializeFilterOptions()
  filterByFavoritos: boolean = false
  filtroAtual: FiltrosEnums
  filter: AssetsFilter = {
    page: 0,
    size: 8,
    favorite: false,
    period: '',
    sort: 'date_desc',
  }
  selectCustom = {
    height: '40px',
    padding: '0 12px 0 8px',
  }
  hasMoreImages: boolean = true
  totalImages: number = 0
  loadingImages = false
  showEmptyState = true

  constructor(
    private assetHub: AssetsItemService,
    private dialog: MatDialog,
    private assetGaleryService: AssetsGalleryService,
    private geradorCriativosService: GeradorCriativosService,
  ) {
    this.assetHub.totalImages$.subscribe(total => {
      this.hasMoreImages = this.filter.size < total
    })
  }

  ngOnInit(): void {
    this.loadingImages = true
    this.loadImages()
    this.loadImagesFavorite()
    this.updateSelectedCount()
    this.updateFavoriteCount()
  }

  private applyFavoriteFilter(filterByFavorites: boolean) {
    this.filter.favorite = filterByFavorites
    this.loadImages()
  }

  applyFilter(tipoEscolhido: string): void {
    this.loadingImages = true
    this.filter.period = tipoEscolhido

    if (
      tipoEscolhido === FiltrosEnums.MAIS_ANTIGO ||
      tipoEscolhido === FiltrosEnums.MAIS_RECENTE
    ) {
      this.filter.sort = tipoEscolhido
    }
    this.loadImages()
  }

  initializeFilterOptions(): MultiSelectData[] {
    return [
      {
        name: 'mais recentes',
        value: FiltrosEnums.MAIS_RECENTE,
        selected: true,
      },
      {
        name: 'mais antigos',
        value: FiltrosEnums.MAIS_ANTIGO,
        selected: false,
      },
      {
        name: 'geradas hoje',
        value: FiltrosEnums.TODAY,
        selected: false,
      },
      {
        name: 'geradas nos últimos 7 dias',
        value: FiltrosEnums.LAST_7DAYS,
        selected: false,
      },
      {
        name: 'geradas nos últimos 15 dias',
        value: FiltrosEnums.LAST_15DAYS,
        selected: false,
      },
    ]
  }

  loadImages(): void {
    this.assetHub.getAssets(this.filter).subscribe({
      next: images => {
        this.showEmptyState = false
        if (images.length === 0) {
          this.showEmptyState = true
        }
        this.updateImages(images)
      },
      error: error => {
        console.error('Erro ao carregar imagens:', error)
        this.loadingImages = false
        this.imagesSubject.next([])
      },
    })
  }

  loadImagesFavorite(): void {
    this.assetHub.getFavorriteAssets().subscribe({
      next: total => {
        this.favoriteCount = total
      },
    })
  }

  useOnNewCreative(): void {
    let imageSelected: AssetsItem | null = null
    this.images$.pipe(take(1)).subscribe(images => {
      imageSelected = images.find(img => img.selected)
    })
    this.geradorCriativosService.handlePanelState(true)
    this.geradorCriativosService.setStepFormValues(5, {
      imageSelected: imageSelected ?? null,
    })
  }

  updateSelectedCount(): void {
    this.images$.subscribe(images => {
      this.selectedCount = images.filter(img => img.selected).length
    })
  }

  updateFavoriteCount(): void {
    this.loadImagesFavorite()
  }

  toggleSelect(image: AssetsItem): void {
    this.images$.subscribe(images => {
      image.selected = !image.selected
      this.onSelectAsset.emit(images)
      this.updateSelectedCount()
    })
  }

  toggleFavorite(image: AssetsItem): void {
    if (!image.favorite && this.favoriteCount >= 15) {
      alert('Você atingiu o limite de 15 imagens favoritas.')
      return
    }

    this.assetGaleryService
      .postFavoriteAsset(image.uuid, !image.favorite)
      .subscribe({
        next: () => {
          image.favorite = !image.favorite
          this.updateFavoriteCount()
        },
        error: (error: unknown) => {
          console.error('Erro ao atualizar favorito:', error)
        },
      })
  }

  loadMore(): void {
    this.filter = {
      ...this.filter,
      size: this.filter.size + 8,
    }
    this.loadImages()
  }

  deleteMutipleAsset() {
    this.updateSelectedCount()
    this.deleteAssets()
  }

  handleDelete(id: string) {
    this.deleteAssets(id)
  }

  handleSelectFilter() {
    const filtroEscolhido = this.formatOptions.find(
      filterOpt => filterOpt.selected,
    ).value

    this.applyFilter(filtroEscolhido)
  }

  toggleFavoriteFilter() {
    this.filterByFavoritos = !this.filterByFavoritos
    this.applyFavoriteFilter(this.filterByFavoritos)
  }

  deleteAssets(id?: string) {
    let headerTitle: string = ''
    let numberSelected: number = 0
    this.images$.pipe(take(1)).subscribe(images => {
      const assetsListToRemove = images.filter(img => img.selected)
      numberSelected = assetsListToRemove.length
      headerTitle =
        assetsListToRemove.length > 1
          ? `(${assetsListToRemove.length}) imagens selecionadas`
          : `${assetsListToRemove[0].name}`
    })

    const dialogRef = this.dialog.open(DialogGenericAssetsComponent, {
      width: '500px',
      data: {
        headerTitle: headerTitle,
        title: 'Tem certeza que deseja excluir?',
        message:
          'Se você continuar, a imagem selecionada será excluída permanentemente. Quer mesmo fazer isso?',
        primaryButton: {
          text: numberSelected > 1 ? 'Excluir imagens' : 'Excluir imagem',
          theme: 'warning-fill',
          returnValue: 'confirmed',
          dataAttrs: {
            element: 'button',
            state: 'activated',
            area: 'galeria_assets',
            section: 'excluir_item',
            label: 'excluir_imagem',
          },
        },
      },
    })

    dialogRef.afterClosed().subscribe(result => {
      if (result === 'confirmed') {
        if (id) this.assetHub.deleteAsset([id])
        else this.confirmDeleteAssets()
        this.hasMoreImages = true
        this.filter.size = 0
      }
    })
  }

  private confirmDeleteAssets() {
    this.updateSelectedCount()
    this.images$.pipe(take(1)).subscribe(images => {
      const assetsListToRemove = images
        .filter(img => img.selected)
        .map(img => img.uuid)
      if (assetsListToRemove.length > 0) {
        this.assetHub.deleteAsset(assetsListToRemove)
      } else {
        console.error('Nenhuma imagem selecionada para excluir.')
      }
    })
  }

  private updateImages(images: AssetsItem[]): void {
    this.loadingImages = false
    this.imagesSubject.next(images)
  }
}
