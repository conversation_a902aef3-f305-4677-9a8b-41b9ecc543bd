<div class="fixed inset-0 bg-black opacity-50 z-40"></div>
<div class="fixed inset-0 flex items-center justify-center z-50">
  <div class="rounded-lg overflow-hidden bg-white shadow-lg w-11/12 max-w-md">
    <header class="border-b border-black/10 h-[70px] flex items-center justify-between p-4 lg:px-6 lg:pt-4 rounded-t-lg">
      <div class="flex gap-3 items-center">
        <div class="truncate max-w-[220px] lg:max-w-full text-40 leading-20 font-semibold font-book text-content-primary-light">
          {{ data.headerTitle }}
        </div>
      </div>
      <button class="ml-auto close delete" aria-label="close dialog" mat-dialog-close></button>
    </header>

    <div class="px-4 lg:px-6 py-10 border-b border-black/10">
      <adtech-typography variant="title" size="large" as="div">
        {{ data.title }}
      </adtech-typography>
      <div class="text-30 leading-23 mt-4 tracking-20 font-regular font-book text-content-primary-light" [innerHTML]="data.message">
      </div>
    </div>

    <div class="flex flex-col lg:flex-row items-center justify-end gap-4 px-4 py-6 lg:p-6 rounded-b-lg">
      <button *ngIf="data.secondaryButton" glbButton class="!uppercase" [theme]="data.secondaryButton.theme" mat-dialog-close (click)="onSecondaryAction()"
        [attr.data-element]="getDataAttributes('secondary')['data-element']"
        [attr.data-state]="getDataAttributes('secondary')['data-state']"
        [attr.data-area]="getDataAttributes('secondary')['data-area']"
        [attr.data-section]="getDataAttributes('secondary')['data-section']"
        [attr.data-label]="getDataAttributes('secondary')['data-label']">
        {{ data.secondaryButton.text }}
      </button>

      <button *ngIf="data.primaryButton" glbButton [theme]="data.primaryButton.theme" (click)="onPrimaryAction()"
        [attr.data-element]="getDataAttributes('primary')['data-element']"
        [attr.data-state]="getDataAttributes('primary')['data-state']"
        [attr.data-area]="getDataAttributes('primary')['data-area']"
        [attr.data-section]="getDataAttributes('primary')['data-section']"
        [attr.data-label]="getDataAttributes('primary')['data-label']">
        {{ data.primaryButton.text }}
      </button>
    </div>
  </div>
</div>