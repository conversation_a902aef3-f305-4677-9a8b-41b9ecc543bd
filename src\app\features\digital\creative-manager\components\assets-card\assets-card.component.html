<div
  class="flex flex-col w-full gap-3 relative group"
  (mouseenter)="showActions = true"
  (mouseleave)="showActions = false"
  #target
>
  <div
    [ngClass]="{
      'bg-gradient-to-r from-blue-500 to-indigo-600 p-[2px] rounded-xl':
        image.selected,
    }"
  >
    <div
      class="w-full flex justify-center items-center overflow-hidden h-[200px] relative bg-[#f5f5f7] rounded-xl p-4"
      [ngClass]="{
        'opacity-50': opacityCard,
        'border-2 border-purple-500 border-dashed': image.specialSelection,
      }"
    >
      <div
        class="h-full w-full transition-all duration-300 ease-in-out rounded-xl"
      >
        <div
          class="flex items-center justify-center w-full h-full rounded-xl overflow-hidden transition-all duration-300 relative"
        >
          <img
            class="h-full w-full object-cover"
            [src]="creativeThumbnail"
            [alt]="image.name"
          />
          <div
            *ngIf="image.favorite"
            class="absolute bottom-2 left-2 w-8 h-8 flex items-center justify-center transition-all duration-300 ease-in-out"
          >
            <div class="relative w-8 h-8">
              <div
                class="absolute inset-0 rounded-full bg-white shadow-md flex items-center justify-center"
              >
                <img
                  vaultSrc="icons/favorite_full.svg"
                  alt="Selo de Favorito para imagem"
                  class="h-5 w-5"
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      <div
        class="absolute w-full flex transition-all duration-500 ease-in-out justify-between inset-0 p-3"
      >
        <div class="flex space-x-2">
          <div
            *ngIf="showCheck"
            [ngClass]="{
              'lg:opacity-100':
                showActions || image.selected || isAssociateCreative,
              'lg:opacity-0 lg:cursor-none': !(
                showActions ||
                image.selected ||
                isAssociateCreative
              ),
              hidden: hiddenActions,
            }"
            class="cursor-pointer h-fit transition-opacity duration-300"
          >
            <div
              (click)="sendCreativeSelectEvent()"
              class="w-[40px] h-[40px] rounded-full flex items-center justify-center bg-white bg-opacity-90 shadow-md transition-all duration-300 ease-in-out"
            >
              <img
                *ngIf="image.selected"
                vaultSrc="icons/select_selected.svg"
                alt="Selected"
                class="h-6 w-6 transition-transform duration-300 ease-in-out"
              />
              <img
                *ngIf="!image.selected"
                vaultSrc="icons/select_unselect.svg"
                alt="Unselected"
                class="h-6 w-6 transition-transform duration-300 ease-in-out"
              />
            </div>
          </div>

          <div
            *ngIf="!hiddenActions && !isAssociateCreative"
            [ngClass]="{
              'lg:opacity-100': showActions,
              'lg:opacity-0 lg:cursor-none': !showActions,
            }"
            class="cursor-pointer h-fit transition-opacity duration-300"
          >
            <div
              (click)="onToggleFavorite()"
              class="w-[40px] h-[40px] rounded-full flex items-center justify-center bg-white bg-opacity-90 shadow-md transition-all duration-300"
            >
              <img
                *ngIf="image.favorite"
                vaultSrc="icons/favorite_full.svg"
                alt="Favorited"
                class="h-6 w-6 transition-transform duration-300 ease-in-out scale-110"
              />
              <img
                *ngIf="!image.favorite"
                vaultSrc="icons/favorite.svg"
                alt="Not favorited"
                class="h-6 w-6 transition-transform duration-300 ease-in-out"
              />
            </div>
          </div>
        </div>
        <div
          class="cursor-pointer h-fit"
          appPopUpMenu
          [menuItems]="menuItems"
          menuSide="right"
          [ngClass]="{
            'lg:opacity-100': showActions,
            'lg:opacity-0 lg:cursor-none': !showActions,
            hidden: isAssociateCreative || hiddenActions,
            'absolute right-0': !showCheck,
          }"
        >
          <div
            class="w-[40px] h-[40px] rounded-full flex items-center justify-center bg-white bg-opacity-90 shadow-md transition-all duration-300 ease-in-out"
          >
            <mat-icon
              class="!w-auto !h-auto text-[30px] text-neutral-600 material-symbols-outlined"
            >
              more_vert
            </mat-icon>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="flex flex-col gap-1" [ngClass]="{ 'opacity-50': opacityCard }">
    <div class="w-full truncate">
      <p class="text-sm font-medium text-gray-800">{{ image.name }}</p>
    </div>
    <div class="flex justify-between items-center">
      <p class="text-xs text-gray-500">{{ image.date | dateConvert }}</p>

      <div
        *ngIf="shouldShowDeletionWarning()"
        class="flex items-center text-red-600 text-xs font-medium"
      >
        <img
          vaultSrc="icons/delete_clock.svg"
          alt="Exclusão"
          class="h-4 w-4 mr-1"
        />
        <ng-container *ngIf="daysUntilDeletion > 0">
          <span *ngIf="daysUntilDeletion === 1">1 dia restante</span>
          <span *ngIf="daysUntilDeletion > 1"
            >{{ daysUntilDeletion }} dias restantes</span
          >
        </ng-container>

        <ng-container *ngIf="daysUntilDeletion <= 0">
          <span *ngIf="daysUntilDeletion === 0">Expirado hoje</span>
          <span *ngIf="daysUntilDeletion === -1">Expirado há 1 dia</span>
          <span *ngIf="daysUntilDeletion < -1"
            >Expirado há {{ -daysUntilDeletion }} dias</span
          >
        </ng-container>
      </div>
    </div>
  </div>
</div>
